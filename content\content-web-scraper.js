// --- START OF FILE content-web-scraper.js ---

/**
 * Web Scraper Content Script
 * Provides comprehensive web scraping functionality for extracting and organizing webpage content
 */

(function() {
    'use strict';

    console.log('Stashy: Web scraper module starting to load...');

    // IMMEDIATELY define the function to prevent timing issues
    // This will be the actual implementation, not a placeholder
    function handleWebScraperNote() {
        if (!noteContainer || !noteText || !noteContainer.classList.contains('visible')) {
            showNote();
            // Show selection modal after a delay to ensure UI is ready
            setTimeout(showWebScraperSelectionModal, 150);
        } else {
            showWebScraperSelectionModal();
        }
    }

    // Make it available immediately
    window.handleWebScraperNote = handleWebScraperNote;
    console.log('Stashy: handleWebScraperNote function defined immediately');

    // Scraping categories configuration
    const SCRAPING_CATEGORIES = {
        emails: {
            id: 'emails',
            name: 'Email Addresses',
            description: 'Extract all email addresses found on the page',
            icon: '📧'
        },
        phones: {
            id: 'phones',
            name: 'Phone Numbers',
            description: 'Find all phone numbers and contact numbers',
            icon: '📱'
        },
        products: {
            id: 'products',
            name: 'Product Information',
            description: 'Extract product titles, prices, descriptions, and ratings',
            icon: '🛒'
        },
        links: {
            id: 'links',
            name: 'All Links and URLs',
            description: 'Capture all links, URLs, and navigation elements',
            icon: '🔗'
        },
        contact: {
            id: 'contact',
            name: 'Contact Information',
            description: 'Find addresses, contact forms, and business information',
            icon: '📞'
        },
        social: {
            id: 'social',
            name: 'Social Media Links',
            description: 'Extract social media profiles and sharing links',
            icon: '📱'
        },
        media: {
            id: 'media',
            name: 'Images and Media',
            description: 'Collect images, videos, and media content information',
            icon: '🎨'
        },
        forms: {
            id: 'forms',
            name: 'Form Data',
            description: 'Extract form structures, fields, and input elements',
            icon: '📋'
        },
        content: {
            id: 'content',
            name: 'Complete Page Content',
            description: 'Extract all text content, headings, and page structure',
            icon: '📄'
        },
        everything: {
            id: 'everything',
            name: 'Everything (Complete Scrape)',
            description: 'Extract all available data from all categories above',
            icon: '🕷️'
        }
    };

    // Configuration for web scraping
    const SCRAPER_CONFIG = {
        // Minimum content length for various data types
        minTextLength: 50,
        minEmailLength: 5,
        minLinkLength: 10,
        
        // Regex patterns for data extraction
        patterns: {
            email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
            phone: /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
            url: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,
            price: /\$[\d,]+\.?\d*|[\d,]+\.?\d*\s*(?:USD|dollars?|€|EUR|£|GBP)/gi,
            socialMedia: /(?:https?:\/\/)?(?:www\.)?(?:facebook|twitter|instagram|linkedin|youtube|tiktok|pinterest)\.com\/[^\s]+/gi
        },

        // Shopping-related selectors
        shoppingSelectors: {
            price: [
                '[class*="price"]', '[id*="price"]', '.cost', '.amount',
                '[data-price]', '.price-current', '.price-now', '.sale-price',
                '.regular-price', '.list-price', '.retail-price'
            ],
            product: [
                '[class*="product"]', '[id*="product"]', '.item', '.listing',
                '[data-product]', '.product-item', '.product-card', '.product-tile'
            ],
            title: [
                'h1', 'h2', '.title', '.name', '.product-name', '.item-name',
                '[class*="title"]', '[class*="name"]', '.heading'
            ],
            description: [
                '.description', '.summary', '.details', '.info',
                '[class*="description"]', '[class*="summary"]', '.product-details'
            ],
            rating: [
                '[class*="rating"]', '[class*="star"]', '[class*="review"]',
                '.score', '.feedback', '[data-rating]'
            ]
        },

        // Content structure selectors
        contentSelectors: {
            main: [
                'main', 'article', '.content', '.main-content', '.post-content',
                '.entry-content', '.article-content', '#content', '.page-content'
            ],
            headings: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
            paragraphs: ['p', '.text', '.paragraph', '.content-text'],
            lists: ['ul', 'ol', '.list', '.items'],
            tables: ['table', '.table', '.data-table'],
            forms: ['form', '.form', '.contact-form']
        }
    };

    /**
     * Shows the web scraper selection modal for users to choose what to scrape
     */
    function showWebScraperSelectionModal() {
        console.log('Stashy: Showing web scraper selection modal');

        // Remove any existing modal
        const existingModal = document.getElementById('stashy-webscraper-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal container
        const modal = document.createElement('div');
        modal.id = 'stashy-webscraper-modal';
        modal.className = 'stashy-modal-overlay';

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'stashy-modal-content stashy-webscraper-modal';

        // Modal header
        const header = document.createElement('div');
        header.className = 'stashy-modal-header';
        header.innerHTML = `
            <h3><span class="stashy-modal-icon">🕷️</span> Web Scraper - Select Data to Extract</h3>
            <button class="stashy-modal-close" id="stashy-webscraper-close">&times;</button>
        `;

        // Modal body with categories
        const body = document.createElement('div');
        body.className = 'stashy-modal-body';

        // Instructions
        const instructions = document.createElement('div');
        instructions.className = 'stashy-webscraper-instructions';
        instructions.innerHTML = `
            <p>Choose what data you want to extract from this webpage. Select one or multiple categories:</p>
        `;

        // Categories container
        const categoriesContainer = document.createElement('div');
        categoriesContainer.className = 'stashy-webscraper-categories';

        // Create category checkboxes
        Object.values(SCRAPING_CATEGORIES).forEach(category => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'stashy-webscraper-category';

            categoryDiv.innerHTML = `
                <label class="stashy-webscraper-category-label">
                    <input type="checkbox" class="stashy-webscraper-checkbox" value="${category.id}"
                           ${category.id === 'everything' ? 'id="stashy-webscraper-everything"' : ''}>
                    <span class="stashy-webscraper-category-icon">${category.icon}</span>
                    <div class="stashy-webscraper-category-info">
                        <div class="stashy-webscraper-category-name">${category.name}</div>
                        <div class="stashy-webscraper-category-desc">${category.description}</div>
                    </div>
                </label>
            `;

            categoriesContainer.appendChild(categoryDiv);
        });

        // Modal footer with buttons
        const footer = document.createElement('div');
        footer.className = 'stashy-modal-footer';
        footer.innerHTML = `
            <div class="stashy-webscraper-footer-left">
                <button class="stashy-btn stashy-btn-secondary" id="stashy-webscraper-select-all">Select All</button>
                <button class="stashy-btn stashy-btn-secondary" id="stashy-webscraper-clear-all">Clear All</button>
            </div>
            <div class="stashy-webscraper-footer-right">
                <button class="stashy-btn stashy-btn-secondary" id="stashy-webscraper-cancel">Cancel</button>
                <button class="stashy-btn stashy-btn-primary" id="stashy-webscraper-extract">Extract Data</button>
            </div>
        `;

        // Assemble modal
        body.appendChild(instructions);
        body.appendChild(categoriesContainer);
        modalContent.appendChild(header);
        modalContent.appendChild(body);
        modalContent.appendChild(footer);
        modal.appendChild(modalContent);

        // Add to page
        document.body.appendChild(modal);

        // Add event listeners
        setupWebScraperModalEvents(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('stashy-modal-show');
        }, 10);
    }

    /**
     * Sets up event listeners for the web scraper modal
     * @param {HTMLElement} modal - The modal element
     */
    function setupWebScraperModalEvents(modal) {
        const closeBtn = modal.querySelector('#stashy-webscraper-close');
        const cancelBtn = modal.querySelector('#stashy-webscraper-cancel');
        const extractBtn = modal.querySelector('#stashy-webscraper-extract');
        const selectAllBtn = modal.querySelector('#stashy-webscraper-select-all');
        const clearAllBtn = modal.querySelector('#stashy-webscraper-clear-all');
        const everythingCheckbox = modal.querySelector('#stashy-webscraper-everything');
        const checkboxes = modal.querySelectorAll('.stashy-webscraper-checkbox');

        // Close modal functions
        const closeModal = () => {
            modal.classList.remove('stashy-modal-show');
            setTimeout(() => modal.remove(), 300);
        };

        // Close button events
        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // Click outside to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });

        // Escape key to close
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Select all button
        selectAllBtn.addEventListener('click', () => {
            checkboxes.forEach(checkbox => {
                if (checkbox.value !== 'everything') {
                    checkbox.checked = true;
                }
            });
            everythingCheckbox.checked = false;
        });

        // Clear all button
        clearAllBtn.addEventListener('click', () => {
            checkboxes.forEach(checkbox => checkbox.checked = false);
        });

        // Everything checkbox logic
        everythingCheckbox.addEventListener('change', () => {
            if (everythingCheckbox.checked) {
                checkboxes.forEach(checkbox => {
                    if (checkbox.value !== 'everything') {
                        checkbox.checked = false;
                    }
                });
            }
        });

        // Other checkboxes logic
        checkboxes.forEach(checkbox => {
            if (checkbox.value !== 'everything') {
                checkbox.addEventListener('change', () => {
                    if (checkbox.checked) {
                        everythingCheckbox.checked = false;
                    }
                });
            }
        });

        // Extract button
        extractBtn.addEventListener('click', () => {
            const selectedCategories = Array.from(checkboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            if (selectedCategories.length === 0) {
                if (typeof showStatus === 'function') {
                    showStatus('⚠️ Please select at least one category to extract', 'warning', 3000);
                }
                return;
            }

            closeModal();

            // Start extraction with selected categories
            setTimeout(() => {
                performWebScrapingWithSelection(selectedCategories);
            }, 100);
        });
    }

    /**
     * Main web scraper function that extracts all relevant content from the current webpage
     * @param {Array} selectedCategories - Array of category IDs to extract (optional, defaults to everything)
     * @returns {Object} Comprehensive scraped data organized by type
     */
    /**
     * Selective web scraping based on user-selected categories
     * @param {Array} selectedCategories - Array of category IDs to extract
     * @returns {Object} Scraped data for selected categories only
     */
    function scrapeWebpageContentSelective(selectedCategories) {
        console.log('Stashy Web Scraper: Starting selective webpage content extraction for:', selectedCategories);

        const scrapedData = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            extractionMethod: 'Selective Web Scraper',
            selectedCategories: selectedCategories
        };

        // Always include metadata for context
        scrapedData.metadata = extractPageMetadata();

        // Extract data based on selected categories
        if (selectedCategories.includes('everything')) {
            // Extract everything
            scrapedData.mainContent = extractMainContent();
            scrapedData.shoppingItems = extractShoppingItems();
            scrapedData.contactInfo = extractContactInformation();
            scrapedData.links = extractLinks();
            scrapedData.structuredData = extractStructuredData();
            scrapedData.media = extractMediaContent();
            scrapedData.forms = extractFormData();
        } else {
            // Extract only selected categories
            if (selectedCategories.includes('content')) {
                scrapedData.mainContent = extractMainContent();
            }
            if (selectedCategories.includes('products')) {
                scrapedData.shoppingItems = extractShoppingItems();
            }
            if (selectedCategories.includes('emails') || selectedCategories.includes('phones') ||
                selectedCategories.includes('contact') || selectedCategories.includes('social')) {
                scrapedData.contactInfo = extractContactInformation();
            }
            if (selectedCategories.includes('links')) {
                scrapedData.links = extractLinks();
            }
            if (selectedCategories.includes('media')) {
                scrapedData.media = extractMediaContent();
            }
            if (selectedCategories.includes('forms')) {
                scrapedData.forms = extractFormData();
            }
        }

        console.log('Stashy Web Scraper: Selective extraction complete', scrapedData);
        return scrapedData;
    }

    function scrapeWebpageContent(selectedCategories = null) {
        console.log('Stashy Web Scraper: Starting comprehensive webpage content extraction');

        const scrapedData = {
            metadata: extractPageMetadata(),
            mainContent: extractMainContent(),
            shoppingItems: extractShoppingItems(),
            contactInfo: extractContactInformation(),
            links: extractLinks(),
            structuredData: extractStructuredData(),
            media: extractMediaContent(),
            forms: extractFormData(),
            timestamp: new Date().toISOString(),
            url: window.location.href,
            extractionMethod: 'Comprehensive Web Scraper'
        };

        console.log('Stashy Web Scraper: Extraction complete', scrapedData);
        return scrapedData;
    }

    /**
     * Extracts page metadata including title, description, and basic info
     * @returns {Object} Page metadata
     */
    function extractPageMetadata() {
        const metadata = {
            title: document.title || '',
            description: '',
            keywords: '',
            author: '',
            siteName: window.location.hostname,
            language: document.documentElement.lang || 'en',
            charset: document.characterSet || 'UTF-8'
        };

        // Extract meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metadata.description = metaDescription.getAttribute('content') || '';
        }

        // Extract meta keywords
        const metaKeywords = document.querySelector('meta[name="keywords"]');
        if (metaKeywords) {
            metadata.keywords = metaKeywords.getAttribute('content') || '';
        }

        // Extract author
        const metaAuthor = document.querySelector('meta[name="author"]');
        if (metaAuthor) {
            metadata.author = metaAuthor.getAttribute('content') || '';
        }

        // Try to get site name from Open Graph
        const ogSiteName = document.querySelector('meta[property="og:site_name"]');
        if (ogSiteName) {
            metadata.siteName = ogSiteName.getAttribute('content') || metadata.siteName;
        }

        return metadata;
    }

    /**
     * Extracts main content using Readability.js and fallback methods
     * @returns {Object} Main content data
     */
    function extractMainContent() {
        const content = {
            text: '',
            headings: [],
            paragraphs: [],
            lists: [],
            tables: [],
            extractionMethod: 'fallback'
        };

        // Try to use existing Readability.js extraction if available
        if (typeof window.extractPageContentWithReadability === 'function') {
            try {
                const readabilityResult = window.extractPageContentWithReadability();
                if (readabilityResult.success && readabilityResult.content) {
                    content.text = readabilityResult.content;
                    content.extractionMethod = 'Readability.js';
                    console.log('Stashy Web Scraper: Used Readability.js for main content');
                }
            } catch (error) {
                console.warn('Stashy Web Scraper: Readability.js failed, using fallback:', error);
            }
        }

        // Fallback: Extract content manually if Readability.js failed
        if (!content.text) {
            content.text = extractTextContent();
            content.extractionMethod = 'Manual extraction';
        }

        // Extract structured content elements
        content.headings = extractHeadings();
        content.paragraphs = extractParagraphs();
        content.lists = extractLists();
        content.tables = extractTables();

        return content;
    }

    /**
     * Performs web scraping with user-selected categories
     * @param {Array} selectedCategories - Array of category IDs to extract
     */
    function performWebScrapingWithSelection(selectedCategories) {
        console.log('Stashy: Starting web scraping with selected categories:', selectedCategories);

        if (!noteText) {
            console.error("Stashy: noteText element not found for web scraper insertion.");
            return;
        }

        // Show progress indicator
        if (typeof showStatus === 'function') {
            showStatus('🕷️ Extracting selected data from webpage...', 'info', 3000);
        }

        try {
            // Extract data based on selected categories
            const scrapedData = scrapeWebpageContentSelective(selectedCategories);

            // Format the data for insertion
            const formattedContent = formatScrapedDataForNote(scrapedData, selectedCategories);

            if (formattedContent) {
                // Focus the note text area
                noteText.focus();

                // Insert formatted content at cursor position
                document.execCommand('insertText', false, formattedContent);

                // Schedule save
                if (typeof scheduleSave === 'function') {
                    scheduleSave();
                }

                // Show success message with detailed results
                const results = generateExtractionSummary(scrapedData, selectedCategories);

                if (typeof showStatus === 'function') {
                    showStatus(`✅ Extraction complete: ${results}`, 'success', 5000);
                }

                console.log('Stashy Web Scraper: Content successfully inserted into note');
            } else {
                if (typeof showStatus === 'function') {
                    showStatus('⚠️ No content found for selected categories', 'warning', 3000);
                }
                console.warn("Stashy Web Scraper: No content found to insert");
            }
        } catch (error) {
            console.error('Stashy Web Scraper: Error during scraping:', error);
            if (typeof showStatus === 'function') {
                showStatus('❌ Error occurred while extracting data', 'error', 5000);
            }
        }
    }

    // Internal function to perform the web scraping (legacy - kept for compatibility)
    function handleWebScraperNoteInternal() {
        if (!noteText) {
            console.error("Stashy: noteText element not found for web scraper insertion.");
            return;
        }

        // Show progress indicator
        if (typeof showStatus === 'function') {
            showStatus('🕷️ Scraping webpage content...', 'info', 3000);
        }

        try {
            // Extract all content
            const scrapedData = scrapeWebpageContent();
            
            // Format the data for insertion
            const formattedContent = formatScrapedDataForNote(scrapedData);
            
            if (formattedContent) {
                // Focus the note text area
                noteText.focus();
                
                // Insert formatted content at cursor position
                document.execCommand('insertText', false, formattedContent);
                
                // Schedule save
                if (typeof scheduleSave === 'function') {
                    scheduleSave();
                }
                
                // Show success message
                if (typeof showStatus === 'function') {
                    showStatus('✅ Web content scraped and added to note!', 'success', 3000);
                }
                
                console.log('Stashy Web Scraper: Content successfully inserted into note');
            } else {
                if (typeof showStatus === 'function') {
                    showStatus('⚠️ No meaningful content found to scrape', 'warning', 3000);
                }
                console.warn("Stashy Web Scraper: No content found to insert");
            }
        } catch (error) {
            console.error('Stashy Web Scraper: Error during scraping:', error);
            if (typeof showStatus === 'function') {
                showStatus('❌ Error occurred while scraping content', 'error', 5000);
            }
        }
    }

    /**
     * Extracts text content from the page using fallback methods
     * @returns {string} Extracted text content
     */
    function extractTextContent() {
        // Try to find main content areas
        for (const selector of SCRAPER_CONFIG.contentSelectors.main) {
            const element = document.querySelector(selector);
            if (element) {
                return cleanText(element.textContent || element.innerText || '');
            }
        }

        // Fallback to body content
        const bodyText = document.body ? (document.body.textContent || document.body.innerText || '') : '';
        return cleanText(bodyText);
    }

    /**
     * Extracts headings from the page
     * @returns {Array} Array of heading objects
     */
    function extractHeadings() {
        const headings = [];
        const headingElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.headings.join(', '));

        headingElements.forEach((heading, index) => {
            const text = cleanText(heading.textContent || heading.innerText || '');
            if (text.length > 0) {
                headings.push({
                    level: parseInt(heading.tagName.charAt(1)) || 1,
                    text: text,
                    id: heading.id || `heading-${index}`,
                    tag: heading.tagName.toLowerCase()
                });
            }
        });

        return headings;
    }

    /**
     * Extracts paragraphs from the page
     * @returns {Array} Array of paragraph texts
     */
    function extractParagraphs() {
        const paragraphs = [];
        const paragraphElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.paragraphs.join(', '));

        paragraphElements.forEach(paragraph => {
            const text = cleanText(paragraph.textContent || paragraph.innerText || '');
            if (text.length >= SCRAPER_CONFIG.minTextLength) {
                paragraphs.push(text);
            }
        });

        return paragraphs;
    }

    /**
     * Extracts lists from the page
     * @returns {Array} Array of list objects
     */
    function extractLists() {
        const lists = [];
        const listElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.lists.join(', '));

        listElements.forEach((list, index) => {
            const items = [];
            const listItems = list.querySelectorAll('li');

            listItems.forEach(item => {
                const text = cleanText(item.textContent || item.innerText || '');
                if (text.length > 0) {
                    items.push(text);
                }
            });

            if (items.length > 0) {
                lists.push({
                    type: list.tagName.toLowerCase(),
                    items: items,
                    id: list.id || `list-${index}`
                });
            }
        });

        return lists;
    }

    /**
     * Extracts tables from the page
     * @returns {Array} Array of table objects
     */
    function extractTables() {
        const tables = [];
        const tableElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.tables.join(', '));

        tableElements.forEach((table, index) => {
            const rows = [];
            const tableRows = table.querySelectorAll('tr');

            tableRows.forEach(row => {
                const cells = [];
                const tableCells = row.querySelectorAll('td, th');

                tableCells.forEach(cell => {
                    const text = cleanText(cell.textContent || cell.innerText || '');
                    cells.push(text);
                });

                if (cells.length > 0) {
                    rows.push(cells);
                }
            });

            if (rows.length > 0) {
                tables.push({
                    rows: rows,
                    id: table.id || `table-${index}`,
                    caption: table.caption ? cleanText(table.caption.textContent || '') : ''
                });
            }
        });

        return tables;
    }

    /**
     * Extracts shopping items and product information
     * @returns {Array} Array of shopping item objects
     */
    function extractShoppingItems() {
        const shoppingItems = [];
        const productElements = document.querySelectorAll(SCRAPER_CONFIG.shoppingSelectors.product.join(', '));

        productElements.forEach((product, index) => {
            const item = {
                title: '',
                price: '',
                description: '',
                rating: '',
                image: '',
                link: ''
            };

            // Extract title
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.title) {
                const titleElement = product.querySelector(selector);
                if (titleElement) {
                    item.title = cleanText(titleElement.textContent || titleElement.innerText || '');
                    break;
                }
            }

            // Extract price
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.price) {
                const priceElement = product.querySelector(selector);
                if (priceElement) {
                    item.price = cleanText(priceElement.textContent || priceElement.innerText || '');
                    break;
                }
            }

            // Extract description
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.description) {
                const descElement = product.querySelector(selector);
                if (descElement) {
                    item.description = cleanText(descElement.textContent || descElement.innerText || '');
                    break;
                }
            }

            // Extract rating
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.rating) {
                const ratingElement = product.querySelector(selector);
                if (ratingElement) {
                    item.rating = cleanText(ratingElement.textContent || ratingElement.innerText || '');
                    break;
                }
            }

            // Extract image
            const imageElement = product.querySelector('img');
            if (imageElement) {
                item.image = imageElement.src || imageElement.getAttribute('data-src') || '';
            }

            // Extract link
            const linkElement = product.querySelector('a');
            if (linkElement) {
                item.link = linkElement.href || '';
            }

            // Only add if we have meaningful data
            if (item.title || item.price || item.description) {
                shoppingItems.push({
                    ...item,
                    id: `product-${index}`
                });
            }
        });

        return shoppingItems;
    }

    /**
     * Extracts contact information including emails, phones, and addresses
     * @returns {Object} Contact information object
     */
    function extractContactInformation() {
        const contactInfo = {
            emails: [],
            phones: [],
            addresses: [],
            socialMedia: []
        };

        const pageText = document.body ? (document.body.textContent || document.body.innerText || '') : '';

        // Extract emails
        const emailMatches = pageText.match(SCRAPER_CONFIG.patterns.email);
        if (emailMatches) {
            contactInfo.emails = [...new Set(emailMatches)]; // Remove duplicates
        }

        // Extract phone numbers
        const phoneMatches = pageText.match(SCRAPER_CONFIG.patterns.phone);
        if (phoneMatches) {
            contactInfo.phones = [...new Set(phoneMatches)]; // Remove duplicates
        }

        // Extract social media links
        const socialMatches = pageText.match(SCRAPER_CONFIG.patterns.socialMedia);
        if (socialMatches) {
            contactInfo.socialMedia = [...new Set(socialMatches)]; // Remove duplicates
        }

        // Try to find address information (basic approach)
        const addressSelectors = [
            '[class*="address"]', '[id*="address"]', '.location', '.contact-info',
            '[class*="location"]', '[class*="contact"]'
        ];

        addressSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const text = cleanText(element.textContent || element.innerText || '');
                if (text.length > 10 && text.length < 200) {
                    contactInfo.addresses.push(text);
                }
            });
        });

        // Remove duplicate addresses
        contactInfo.addresses = [...new Set(contactInfo.addresses)];

        return contactInfo;
    }

    /**
     * Extracts links from the page
     * @returns {Array} Array of link objects
     */
    function extractLinks() {
        const links = [];
        const linkElements = document.querySelectorAll('a[href]');

        linkElements.forEach((link, index) => {
            const href = link.href;
            const text = cleanText(link.textContent || link.innerText || '');

            // Skip empty links, javascript links, and very short text
            if (href && !href.startsWith('javascript:') && !href.startsWith('mailto:') &&
                text.length >= SCRAPER_CONFIG.minLinkLength) {

                links.push({
                    url: href,
                    text: text,
                    title: link.title || '',
                    target: link.target || '',
                    id: `link-${index}`
                });
            }
        });

        return links;
    }

    /**
     * Extracts structured data from JSON-LD and microdata
     * @returns {Object} Structured data object
     */
    function extractStructuredData() {
        const structuredData = {
            jsonLd: [],
            microdata: [],
            openGraph: {},
            twitterCard: {}
        };

        // Extract JSON-LD data
        const jsonLdElements = document.querySelectorAll('script[type="application/ld+json"]');
        jsonLdElements.forEach(script => {
            try {
                const data = JSON.parse(script.textContent || '');
                structuredData.jsonLd.push(data);
            } catch (error) {
                console.warn('Stashy Web Scraper: Invalid JSON-LD data:', error);
            }
        });

        // Extract Open Graph data
        const ogElements = document.querySelectorAll('meta[property^="og:"]');
        ogElements.forEach(meta => {
            const property = meta.getAttribute('property');
            const content = meta.getAttribute('content');
            if (property && content) {
                structuredData.openGraph[property] = content;
            }
        });

        // Extract Twitter Card data
        const twitterElements = document.querySelectorAll('meta[name^="twitter:"]');
        twitterElements.forEach(meta => {
            const name = meta.getAttribute('name');
            const content = meta.getAttribute('content');
            if (name && content) {
                structuredData.twitterCard[name] = content;
            }
        });

        return structuredData;
    }

    /**
     * Extracts media content information
     * @returns {Object} Media content object
     */
    function extractMediaContent() {
        const media = {
            images: [],
            videos: [],
            audio: []
        };

        // Extract images
        const imageElements = document.querySelectorAll('img');
        imageElements.forEach((img, index) => {
            const src = img.src || img.getAttribute('data-src') || '';
            const alt = img.alt || '';

            if (src && !src.startsWith('data:')) {
                media.images.push({
                    src: src,
                    alt: alt,
                    title: img.title || '',
                    width: img.width || '',
                    height: img.height || '',
                    id: `image-${index}`
                });
            }
        });

        // Extract videos
        const videoElements = document.querySelectorAll('video');
        videoElements.forEach((video, index) => {
            const src = video.src || video.currentSrc || '';

            if (src) {
                media.videos.push({
                    src: src,
                    title: video.title || '',
                    duration: video.duration || '',
                    poster: video.poster || '',
                    id: `video-${index}`
                });
            }
        });

        // Extract audio
        const audioElements = document.querySelectorAll('audio');
        audioElements.forEach((audio, index) => {
            const src = audio.src || audio.currentSrc || '';

            if (src) {
                media.audio.push({
                    src: src,
                    title: audio.title || '',
                    duration: audio.duration || '',
                    id: `audio-${index}`
                });
            }
        });

        return media;
    }

    /**
     * Extracts form data and structure
     * @returns {Array} Array of form objects
     */
    function extractFormData() {
        const forms = [];
        const formElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.forms.join(', '));

        formElements.forEach((form, index) => {
            const formData = {
                action: form.action || '',
                method: form.method || 'GET',
                fields: [],
                id: form.id || `form-${index}`
            };

            // Extract form fields
            const fieldElements = form.querySelectorAll('input, textarea, select');
            fieldElements.forEach(field => {
                const fieldInfo = {
                    type: field.type || field.tagName.toLowerCase(),
                    name: field.name || '',
                    placeholder: field.placeholder || '',
                    label: '',
                    required: field.required || false
                };

                // Try to find associated label
                const label = form.querySelector(`label[for="${field.id}"]`) ||
                             field.closest('label') ||
                             field.previousElementSibling;

                if (label && label.tagName === 'LABEL') {
                    fieldInfo.label = cleanText(label.textContent || label.innerText || '');
                }

                formData.fields.push(fieldInfo);
            });

            if (formData.fields.length > 0) {
                forms.push(formData);
            }
        });

        return forms;
    }

    /**
     * Formats scraped data for insertion into note with Excel-like structure
     * @param {Object} scrapedData - The scraped data object
     * @param {Array} selectedCategories - Array of selected category IDs (optional)
     * @returns {string} Formatted text for note insertion
     */
    function formatScrapedDataForNote(scrapedData, selectedCategories = null) {
        let formattedContent = '';

        // Header with timestamp and URL
        formattedContent += `🕷️ WEB SCRAPER RESULTS\n`;
        formattedContent += `${'═'.repeat(50)}\n`;
        formattedContent += `📅 Scraped: ${new Date(scrapedData.timestamp).toLocaleString()}\n`;
        formattedContent += `🌐 URL: ${scrapedData.url}\n`;
        formattedContent += `⚙️ Method: ${scrapedData.extractionMethod}\n`;

        // Show selected categories if available
        if (selectedCategories && selectedCategories.length > 0) {
            const categoryNames = selectedCategories.map(id =>
                SCRAPING_CATEGORIES[id]?.name || id
            ).join(', ');
            formattedContent += `📋 Selected: ${categoryNames}\n`;
        }
        formattedContent += '\n';

        // Determine what to show based on selected categories
        const showEverything = !selectedCategories || selectedCategories.includes('everything');
        const shouldShow = (category) => showEverything || selectedCategories.includes(category);

        // Page Metadata Section (always shown for context)
        if (scrapedData.metadata && scrapedData.metadata.title) {
            formattedContent += `📄 PAGE INFORMATION\n`;
            formattedContent += `${'─'.repeat(30)}\n`;
            formattedContent += `Title: ${scrapedData.metadata.title}\n`;
            if (scrapedData.metadata.description) {
                formattedContent += `Description: ${scrapedData.metadata.description}\n`;
            }
            if (scrapedData.metadata.author) {
                formattedContent += `Author: ${scrapedData.metadata.author}\n`;
            }
            if (scrapedData.metadata.keywords) {
                formattedContent += `Keywords: ${scrapedData.metadata.keywords}\n`;
            }
            formattedContent += `Site: ${scrapedData.metadata.siteName}\n`;
            formattedContent += `Language: ${scrapedData.metadata.language}\n\n`;
        }

        // Main Content Section
        if (shouldShow('content') && scrapedData.mainContent && scrapedData.mainContent.text) {
            formattedContent += `📝 MAIN CONTENT\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            // Add headings if available
            if (scrapedData.mainContent.headings && scrapedData.mainContent.headings.length > 0) {
                formattedContent += `\n📋 HEADINGS:\n`;
                scrapedData.mainContent.headings.forEach((heading, index) => {
                    const indent = '  '.repeat(heading.level - 1);
                    formattedContent += `${indent}${index + 1}. ${heading.text}\n`;
                });
                formattedContent += '\n';
            }

            // Add complete main text content (no truncation)
            formattedContent += `Content: ${scrapedData.mainContent.text}`;
            formattedContent += '\n\n';
        }

        // Shopping Items Section
        if (shouldShow('products') && scrapedData.shoppingItems && scrapedData.shoppingItems.length > 0) {
            formattedContent += `🛒 SHOPPING ITEMS (${scrapedData.shoppingItems.length} found)\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            scrapedData.shoppingItems.forEach((item, index) => {
                formattedContent += `${index + 1}. ${item.title || 'Untitled Product'}\n`;
                if (item.price) formattedContent += `   💰 Price: ${item.price}\n`;
                if (item.rating) formattedContent += `   ⭐ Rating: ${item.rating}\n`;
                if (item.description) {
                    formattedContent += `   📝 Description: ${item.description}\n`;
                }
                if (item.link) formattedContent += `   🔗 Link: ${item.link}\n`;
                formattedContent += '\n';
            });
        }

        // Contact Information Section (selective based on categories)
        if (scrapedData.contactInfo) {
            const showEmails = shouldShow('emails');
            const showPhones = shouldShow('phones');
            const showContact = shouldShow('contact');
            const showSocial = shouldShow('social');

            const hasRelevantContactInfo =
                (showEmails && scrapedData.contactInfo.emails.length > 0) ||
                (showPhones && scrapedData.contactInfo.phones.length > 0) ||
                (showContact && scrapedData.contactInfo.addresses.length > 0) ||
                (showSocial && scrapedData.contactInfo.socialMedia.length > 0);

            if (hasRelevantContactInfo) {
                formattedContent += `📞 CONTACT INFORMATION\n`;
                formattedContent += `${'─'.repeat(30)}\n`;

                if (showEmails && scrapedData.contactInfo.emails.length > 0) {
                    formattedContent += `📧 Email Addresses (${scrapedData.contactInfo.emails.length} found):\n`;
                    scrapedData.contactInfo.emails.forEach(email => {
                        formattedContent += `   • ${email}\n`;
                    });
                    formattedContent += '\n';
                }

                if (showPhones && scrapedData.contactInfo.phones.length > 0) {
                    formattedContent += `📱 Phone Numbers (${scrapedData.contactInfo.phones.length} found):\n`;
                    scrapedData.contactInfo.phones.forEach(phone => {
                        formattedContent += `   • ${phone}\n`;
                    });
                    formattedContent += '\n';
                }

                if (showContact && scrapedData.contactInfo.addresses.length > 0) {
                    formattedContent += `📍 Addresses (${scrapedData.contactInfo.addresses.length} found):\n`;
                    scrapedData.contactInfo.addresses.forEach(address => {
                        formattedContent += `   • ${address}\n`;
                    });
                    formattedContent += '\n';
                }

                if (showSocial && scrapedData.contactInfo.socialMedia.length > 0) {
                    formattedContent += `📱 Social Media Links (${scrapedData.contactInfo.socialMedia.length} found):\n`;
                    scrapedData.contactInfo.socialMedia.forEach(social => {
                        formattedContent += `   • ${social}\n`;
                    });
                    formattedContent += '\n';
                }
            }
        }

        // Links Section (complete list, no truncation)
        if (shouldShow('links') && scrapedData.links && scrapedData.links.length > 0) {
            formattedContent += `🔗 LINKS (${scrapedData.links.length} found)\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            scrapedData.links.forEach((link, index) => {
                formattedContent += `${index + 1}. ${link.text}\n`;
                formattedContent += `   🌐 ${link.url}\n`;
                if (link.title) formattedContent += `   📝 ${link.title}\n`;
                formattedContent += '\n';
            });
        }

        // Media Content Section (summary)
        if (shouldShow('media') && scrapedData.media) {
            const hasMedia = scrapedData.media.images.length > 0 ||
                           scrapedData.media.videos.length > 0 ||
                           scrapedData.media.audio.length > 0;

            if (hasMedia) {
                formattedContent += `🎨 MEDIA CONTENT\n`;
                formattedContent += `${'─'.repeat(30)}\n`;

                if (scrapedData.media.images.length > 0) {
                    formattedContent += `🖼️ Images: ${scrapedData.media.images.length} found\n`;
                }
                if (scrapedData.media.videos.length > 0) {
                    formattedContent += `🎥 Videos: ${scrapedData.media.videos.length} found\n`;
                }
                if (scrapedData.media.audio.length > 0) {
                    formattedContent += `🎵 Audio: ${scrapedData.media.audio.length} found\n`;
                }
                formattedContent += '\n';
            }
        }

        // Forms Section (summary)
        if (shouldShow('forms') && scrapedData.forms && scrapedData.forms.length > 0) {
            formattedContent += `📋 FORMS (${scrapedData.forms.length} found)\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            scrapedData.forms.forEach((form, index) => {
                formattedContent += `${index + 1}. Form with ${form.fields.length} fields\n`;
                if (form.action) formattedContent += `   🎯 Action: ${form.action}\n`;
                formattedContent += `   📝 Method: ${form.method}\n`;

                // Show field types
                const fieldTypes = form.fields.map(field => field.type).filter((type, index, arr) => arr.indexOf(type) === index);
                formattedContent += `   🔧 Field Types: ${fieldTypes.join(', ')}\n\n`;
            });
        }

        // Footer
        formattedContent += `${'═'.repeat(50)}\n`;
        formattedContent += `✅ Web scraping completed successfully\n\n`;

        return formattedContent;
    }

    /**
     * Generates a summary of extraction results for user feedback
     * @param {Object} scrapedData - The scraped data object
     * @param {Array} selectedCategories - Array of selected category IDs
     * @returns {string} Summary of what was found
     */
    function generateExtractionSummary(scrapedData, selectedCategories) {
        const results = [];

        if (selectedCategories.includes('everything')) {
            // Count all items for everything mode
            const counts = [];
            if (scrapedData.shoppingItems?.length > 0) counts.push(`${scrapedData.shoppingItems.length} products`);
            if (scrapedData.contactInfo?.emails?.length > 0) counts.push(`${scrapedData.contactInfo.emails.length} emails`);
            if (scrapedData.contactInfo?.phones?.length > 0) counts.push(`${scrapedData.contactInfo.phones.length} phones`);
            if (scrapedData.links?.length > 0) counts.push(`${scrapedData.links.length} links`);
            if (scrapedData.media?.images?.length > 0) counts.push(`${scrapedData.media.images.length} images`);
            if (scrapedData.forms?.length > 0) counts.push(`${scrapedData.forms.length} forms`);

            return counts.length > 0 ? counts.join(', ') : 'Complete page content';
        } else {
            // Count items for selected categories
            selectedCategories.forEach(category => {
                switch (category) {
                    case 'emails':
                        if (scrapedData.contactInfo?.emails?.length > 0) {
                            results.push(`${scrapedData.contactInfo.emails.length} emails`);
                        }
                        break;
                    case 'phones':
                        if (scrapedData.contactInfo?.phones?.length > 0) {
                            results.push(`${scrapedData.contactInfo.phones.length} phones`);
                        }
                        break;
                    case 'products':
                        if (scrapedData.shoppingItems?.length > 0) {
                            results.push(`${scrapedData.shoppingItems.length} products`);
                        }
                        break;
                    case 'links':
                        if (scrapedData.links?.length > 0) {
                            results.push(`${scrapedData.links.length} links`);
                        }
                        break;
                    case 'contact':
                        if (scrapedData.contactInfo?.addresses?.length > 0) {
                            results.push(`${scrapedData.contactInfo.addresses.length} addresses`);
                        }
                        break;
                    case 'social':
                        if (scrapedData.contactInfo?.socialMedia?.length > 0) {
                            results.push(`${scrapedData.contactInfo.socialMedia.length} social links`);
                        }
                        break;
                    case 'media':
                        const mediaCount = (scrapedData.media?.images?.length || 0) +
                                         (scrapedData.media?.videos?.length || 0) +
                                         (scrapedData.media?.audio?.length || 0);
                        if (mediaCount > 0) {
                            results.push(`${mediaCount} media items`);
                        }
                        break;
                    case 'forms':
                        if (scrapedData.forms?.length > 0) {
                            results.push(`${scrapedData.forms.length} forms`);
                        }
                        break;
                    case 'content':
                        if (scrapedData.mainContent?.text) {
                            results.push('page content');
                        }
                        break;
                }
            });

            return results.length > 0 ? results.join(', ') : 'No data found for selected categories';
        }
    }

    /**
     * Utility function to clean and normalize text
     * @param {string} text - Text to clean
     * @returns {string} Cleaned text
     */
    function cleanText(text) {
        if (!text) return '';

        return text
            .replace(/\s+/g, ' ')           // Replace multiple whitespace with single space
            .replace(/\n\s*\n/g, '\n')      // Replace multiple newlines with single newline
            .trim();                        // Remove leading/trailing whitespace
    }

    // Create a namespace for the web scraper and store the implementation
    window.StashyWebScraper = {
        handleWebScraperNote: handleWebScraperNote,
        handleWebScraperNoteInternal: handleWebScraperNoteInternal,
        scrapeWebpageContent: scrapeWebpageContent,
        formatScrapedDataForNote: formatScrapedDataForNote
    };

    // Also make the main function directly available for backward compatibility
    window.handleWebScraperNote = handleWebScraperNote;

    console.log('Stashy: Web scraper module fully loaded and handleWebScraperNote function is now available');

})();

// --- END OF FILE content-web-scraper.js ---
