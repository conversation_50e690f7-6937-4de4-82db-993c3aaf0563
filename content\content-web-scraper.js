// --- START OF FILE content-web-scraper.js ---

/**
 * Web Scraper Content Script
 * Provides comprehensive web scraping functionality for extracting and organizing webpage content
 */

(function() {
    'use strict';

    // Configuration for web scraping
    const SCRAPER_CONFIG = {
        // Minimum content length for various data types
        minTextLength: 50,
        minEmailLength: 5,
        minLinkLength: 10,
        
        // Regex patterns for data extraction
        patterns: {
            email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
            phone: /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g,
            url: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,
            price: /\$[\d,]+\.?\d*|[\d,]+\.?\d*\s*(?:USD|dollars?|€|EUR|£|GBP)/gi,
            socialMedia: /(?:https?:\/\/)?(?:www\.)?(?:facebook|twitter|instagram|linkedin|youtube|tiktok|pinterest)\.com\/[^\s]+/gi
        },

        // Shopping-related selectors
        shoppingSelectors: {
            price: [
                '[class*="price"]', '[id*="price"]', '.cost', '.amount',
                '[data-price]', '.price-current', '.price-now', '.sale-price',
                '.regular-price', '.list-price', '.retail-price'
            ],
            product: [
                '[class*="product"]', '[id*="product"]', '.item', '.listing',
                '[data-product]', '.product-item', '.product-card', '.product-tile'
            ],
            title: [
                'h1', 'h2', '.title', '.name', '.product-name', '.item-name',
                '[class*="title"]', '[class*="name"]', '.heading'
            ],
            description: [
                '.description', '.summary', '.details', '.info',
                '[class*="description"]', '[class*="summary"]', '.product-details'
            ],
            rating: [
                '[class*="rating"]', '[class*="star"]', '[class*="review"]',
                '.score', '.feedback', '[data-rating]'
            ]
        },

        // Content structure selectors
        contentSelectors: {
            main: [
                'main', 'article', '.content', '.main-content', '.post-content',
                '.entry-content', '.article-content', '#content', '.page-content'
            ],
            headings: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
            paragraphs: ['p', '.text', '.paragraph', '.content-text'],
            lists: ['ul', 'ol', '.list', '.items'],
            tables: ['table', '.table', '.data-table'],
            forms: ['form', '.form', '.contact-form']
        }
    };

    /**
     * Main web scraper function that extracts all relevant content from the current webpage
     * @returns {Object} Comprehensive scraped data organized by type
     */
    function scrapeWebpageContent() {
        console.log('Stashy Web Scraper: Starting comprehensive webpage content extraction');
        
        const scrapedData = {
            metadata: extractPageMetadata(),
            mainContent: extractMainContent(),
            shoppingItems: extractShoppingItems(),
            contactInfo: extractContactInformation(),
            links: extractLinks(),
            structuredData: extractStructuredData(),
            media: extractMediaContent(),
            forms: extractFormData(),
            timestamp: new Date().toISOString(),
            url: window.location.href,
            extractionMethod: 'Comprehensive Web Scraper'
        };

        console.log('Stashy Web Scraper: Extraction complete', scrapedData);
        return scrapedData;
    }

    /**
     * Extracts page metadata including title, description, and basic info
     * @returns {Object} Page metadata
     */
    function extractPageMetadata() {
        const metadata = {
            title: document.title || '',
            description: '',
            keywords: '',
            author: '',
            siteName: window.location.hostname,
            language: document.documentElement.lang || 'en',
            charset: document.characterSet || 'UTF-8'
        };

        // Extract meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metadata.description = metaDescription.getAttribute('content') || '';
        }

        // Extract meta keywords
        const metaKeywords = document.querySelector('meta[name="keywords"]');
        if (metaKeywords) {
            metadata.keywords = metaKeywords.getAttribute('content') || '';
        }

        // Extract author
        const metaAuthor = document.querySelector('meta[name="author"]');
        if (metaAuthor) {
            metadata.author = metaAuthor.getAttribute('content') || '';
        }

        // Try to get site name from Open Graph
        const ogSiteName = document.querySelector('meta[property="og:site_name"]');
        if (ogSiteName) {
            metadata.siteName = ogSiteName.getAttribute('content') || metadata.siteName;
        }

        return metadata;
    }

    /**
     * Extracts main content using Readability.js and fallback methods
     * @returns {Object} Main content data
     */
    function extractMainContent() {
        const content = {
            text: '',
            headings: [],
            paragraphs: [],
            lists: [],
            tables: [],
            extractionMethod: 'fallback'
        };

        // Try to use existing Readability.js extraction if available
        if (typeof window.extractPageContentWithReadability === 'function') {
            try {
                const readabilityResult = window.extractPageContentWithReadability();
                if (readabilityResult.success && readabilityResult.content) {
                    content.text = readabilityResult.content;
                    content.extractionMethod = 'Readability.js';
                    console.log('Stashy Web Scraper: Used Readability.js for main content');
                }
            } catch (error) {
                console.warn('Stashy Web Scraper: Readability.js failed, using fallback:', error);
            }
        }

        // Fallback: Extract content manually if Readability.js failed
        if (!content.text) {
            content.text = extractTextContent();
            content.extractionMethod = 'Manual extraction';
        }

        // Extract structured content elements
        content.headings = extractHeadings();
        content.paragraphs = extractParagraphs();
        content.lists = extractLists();
        content.tables = extractTables();

        return content;
    }

    /**
     * Handles the web scraper button click - main entry point
     */
    function handleWebScraperNote() {
        if (!noteContainer || !noteText || !noteContainer.classList.contains('visible')) {
            showNote();
            // Use internal function after a delay to ensure UI is ready
            setTimeout(handleWebScraperNoteInternal, 150);
        } else {
            handleWebScraperNoteInternal();
        }
    }

    // Internal function to perform the web scraping
    function handleWebScraperNoteInternal() {
        if (!noteText) {
            console.error("Stashy: noteText element not found for web scraper insertion.");
            return;
        }

        // Show progress indicator
        if (typeof showStatus === 'function') {
            showStatus('🕷️ Scraping webpage content...', 'info', 3000);
        }

        try {
            // Extract all content
            const scrapedData = scrapeWebpageContent();
            
            // Format the data for insertion
            const formattedContent = formatScrapedDataForNote(scrapedData);
            
            if (formattedContent) {
                // Focus the note text area
                noteText.focus();
                
                // Insert formatted content at cursor position
                document.execCommand('insertText', false, formattedContent);
                
                // Schedule save
                if (typeof scheduleSave === 'function') {
                    scheduleSave();
                }
                
                // Show success message
                if (typeof showStatus === 'function') {
                    showStatus('✅ Web content scraped and added to note!', 'success', 3000);
                }
                
                console.log('Stashy Web Scraper: Content successfully inserted into note');
            } else {
                if (typeof showStatus === 'function') {
                    showStatus('⚠️ No meaningful content found to scrape', 'warning', 3000);
                }
                console.warn("Stashy Web Scraper: No content found to insert");
            }
        } catch (error) {
            console.error('Stashy Web Scraper: Error during scraping:', error);
            if (typeof showStatus === 'function') {
                showStatus('❌ Error occurred while scraping content', 'error', 5000);
            }
        }
    }

    /**
     * Extracts text content from the page using fallback methods
     * @returns {string} Extracted text content
     */
    function extractTextContent() {
        // Try to find main content areas
        for (const selector of SCRAPER_CONFIG.contentSelectors.main) {
            const element = document.querySelector(selector);
            if (element) {
                return cleanText(element.textContent || element.innerText || '');
            }
        }

        // Fallback to body content
        const bodyText = document.body ? (document.body.textContent || document.body.innerText || '') : '';
        return cleanText(bodyText);
    }

    /**
     * Extracts headings from the page
     * @returns {Array} Array of heading objects
     */
    function extractHeadings() {
        const headings = [];
        const headingElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.headings.join(', '));

        headingElements.forEach((heading, index) => {
            const text = cleanText(heading.textContent || heading.innerText || '');
            if (text.length > 0) {
                headings.push({
                    level: parseInt(heading.tagName.charAt(1)) || 1,
                    text: text,
                    id: heading.id || `heading-${index}`,
                    tag: heading.tagName.toLowerCase()
                });
            }
        });

        return headings;
    }

    /**
     * Extracts paragraphs from the page
     * @returns {Array} Array of paragraph texts
     */
    function extractParagraphs() {
        const paragraphs = [];
        const paragraphElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.paragraphs.join(', '));

        paragraphElements.forEach(paragraph => {
            const text = cleanText(paragraph.textContent || paragraph.innerText || '');
            if (text.length >= SCRAPER_CONFIG.minTextLength) {
                paragraphs.push(text);
            }
        });

        return paragraphs;
    }

    /**
     * Extracts lists from the page
     * @returns {Array} Array of list objects
     */
    function extractLists() {
        const lists = [];
        const listElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.lists.join(', '));

        listElements.forEach((list, index) => {
            const items = [];
            const listItems = list.querySelectorAll('li');

            listItems.forEach(item => {
                const text = cleanText(item.textContent || item.innerText || '');
                if (text.length > 0) {
                    items.push(text);
                }
            });

            if (items.length > 0) {
                lists.push({
                    type: list.tagName.toLowerCase(),
                    items: items,
                    id: list.id || `list-${index}`
                });
            }
        });

        return lists;
    }

    /**
     * Extracts tables from the page
     * @returns {Array} Array of table objects
     */
    function extractTables() {
        const tables = [];
        const tableElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.tables.join(', '));

        tableElements.forEach((table, index) => {
            const rows = [];
            const tableRows = table.querySelectorAll('tr');

            tableRows.forEach(row => {
                const cells = [];
                const tableCells = row.querySelectorAll('td, th');

                tableCells.forEach(cell => {
                    const text = cleanText(cell.textContent || cell.innerText || '');
                    cells.push(text);
                });

                if (cells.length > 0) {
                    rows.push(cells);
                }
            });

            if (rows.length > 0) {
                tables.push({
                    rows: rows,
                    id: table.id || `table-${index}`,
                    caption: table.caption ? cleanText(table.caption.textContent || '') : ''
                });
            }
        });

        return tables;
    }

    /**
     * Extracts shopping items and product information
     * @returns {Array} Array of shopping item objects
     */
    function extractShoppingItems() {
        const shoppingItems = [];
        const productElements = document.querySelectorAll(SCRAPER_CONFIG.shoppingSelectors.product.join(', '));

        productElements.forEach((product, index) => {
            const item = {
                title: '',
                price: '',
                description: '',
                rating: '',
                image: '',
                link: ''
            };

            // Extract title
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.title) {
                const titleElement = product.querySelector(selector);
                if (titleElement) {
                    item.title = cleanText(titleElement.textContent || titleElement.innerText || '');
                    break;
                }
            }

            // Extract price
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.price) {
                const priceElement = product.querySelector(selector);
                if (priceElement) {
                    item.price = cleanText(priceElement.textContent || priceElement.innerText || '');
                    break;
                }
            }

            // Extract description
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.description) {
                const descElement = product.querySelector(selector);
                if (descElement) {
                    item.description = cleanText(descElement.textContent || descElement.innerText || '');
                    break;
                }
            }

            // Extract rating
            for (const selector of SCRAPER_CONFIG.shoppingSelectors.rating) {
                const ratingElement = product.querySelector(selector);
                if (ratingElement) {
                    item.rating = cleanText(ratingElement.textContent || ratingElement.innerText || '');
                    break;
                }
            }

            // Extract image
            const imageElement = product.querySelector('img');
            if (imageElement) {
                item.image = imageElement.src || imageElement.getAttribute('data-src') || '';
            }

            // Extract link
            const linkElement = product.querySelector('a');
            if (linkElement) {
                item.link = linkElement.href || '';
            }

            // Only add if we have meaningful data
            if (item.title || item.price || item.description) {
                shoppingItems.push({
                    ...item,
                    id: `product-${index}`
                });
            }
        });

        return shoppingItems;
    }

    /**
     * Extracts contact information including emails, phones, and addresses
     * @returns {Object} Contact information object
     */
    function extractContactInformation() {
        const contactInfo = {
            emails: [],
            phones: [],
            addresses: [],
            socialMedia: []
        };

        const pageText = document.body ? (document.body.textContent || document.body.innerText || '') : '';

        // Extract emails
        const emailMatches = pageText.match(SCRAPER_CONFIG.patterns.email);
        if (emailMatches) {
            contactInfo.emails = [...new Set(emailMatches)]; // Remove duplicates
        }

        // Extract phone numbers
        const phoneMatches = pageText.match(SCRAPER_CONFIG.patterns.phone);
        if (phoneMatches) {
            contactInfo.phones = [...new Set(phoneMatches)]; // Remove duplicates
        }

        // Extract social media links
        const socialMatches = pageText.match(SCRAPER_CONFIG.patterns.socialMedia);
        if (socialMatches) {
            contactInfo.socialMedia = [...new Set(socialMatches)]; // Remove duplicates
        }

        // Try to find address information (basic approach)
        const addressSelectors = [
            '[class*="address"]', '[id*="address"]', '.location', '.contact-info',
            '[class*="location"]', '[class*="contact"]'
        ];

        addressSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const text = cleanText(element.textContent || element.innerText || '');
                if (text.length > 10 && text.length < 200) {
                    contactInfo.addresses.push(text);
                }
            });
        });

        // Remove duplicate addresses
        contactInfo.addresses = [...new Set(contactInfo.addresses)];

        return contactInfo;
    }

    /**
     * Extracts links from the page
     * @returns {Array} Array of link objects
     */
    function extractLinks() {
        const links = [];
        const linkElements = document.querySelectorAll('a[href]');

        linkElements.forEach((link, index) => {
            const href = link.href;
            const text = cleanText(link.textContent || link.innerText || '');

            // Skip empty links, javascript links, and very short text
            if (href && !href.startsWith('javascript:') && !href.startsWith('mailto:') &&
                text.length >= SCRAPER_CONFIG.minLinkLength) {

                links.push({
                    url: href,
                    text: text,
                    title: link.title || '',
                    target: link.target || '',
                    id: `link-${index}`
                });
            }
        });

        return links;
    }

    /**
     * Extracts structured data from JSON-LD and microdata
     * @returns {Object} Structured data object
     */
    function extractStructuredData() {
        const structuredData = {
            jsonLd: [],
            microdata: [],
            openGraph: {},
            twitterCard: {}
        };

        // Extract JSON-LD data
        const jsonLdElements = document.querySelectorAll('script[type="application/ld+json"]');
        jsonLdElements.forEach(script => {
            try {
                const data = JSON.parse(script.textContent || '');
                structuredData.jsonLd.push(data);
            } catch (error) {
                console.warn('Stashy Web Scraper: Invalid JSON-LD data:', error);
            }
        });

        // Extract Open Graph data
        const ogElements = document.querySelectorAll('meta[property^="og:"]');
        ogElements.forEach(meta => {
            const property = meta.getAttribute('property');
            const content = meta.getAttribute('content');
            if (property && content) {
                structuredData.openGraph[property] = content;
            }
        });

        // Extract Twitter Card data
        const twitterElements = document.querySelectorAll('meta[name^="twitter:"]');
        twitterElements.forEach(meta => {
            const name = meta.getAttribute('name');
            const content = meta.getAttribute('content');
            if (name && content) {
                structuredData.twitterCard[name] = content;
            }
        });

        return structuredData;
    }

    /**
     * Extracts media content information
     * @returns {Object} Media content object
     */
    function extractMediaContent() {
        const media = {
            images: [],
            videos: [],
            audio: []
        };

        // Extract images
        const imageElements = document.querySelectorAll('img');
        imageElements.forEach((img, index) => {
            const src = img.src || img.getAttribute('data-src') || '';
            const alt = img.alt || '';

            if (src && !src.startsWith('data:')) {
                media.images.push({
                    src: src,
                    alt: alt,
                    title: img.title || '',
                    width: img.width || '',
                    height: img.height || '',
                    id: `image-${index}`
                });
            }
        });

        // Extract videos
        const videoElements = document.querySelectorAll('video');
        videoElements.forEach((video, index) => {
            const src = video.src || video.currentSrc || '';

            if (src) {
                media.videos.push({
                    src: src,
                    title: video.title || '',
                    duration: video.duration || '',
                    poster: video.poster || '',
                    id: `video-${index}`
                });
            }
        });

        // Extract audio
        const audioElements = document.querySelectorAll('audio');
        audioElements.forEach((audio, index) => {
            const src = audio.src || audio.currentSrc || '';

            if (src) {
                media.audio.push({
                    src: src,
                    title: audio.title || '',
                    duration: audio.duration || '',
                    id: `audio-${index}`
                });
            }
        });

        return media;
    }

    /**
     * Extracts form data and structure
     * @returns {Array} Array of form objects
     */
    function extractFormData() {
        const forms = [];
        const formElements = document.querySelectorAll(SCRAPER_CONFIG.contentSelectors.forms.join(', '));

        formElements.forEach((form, index) => {
            const formData = {
                action: form.action || '',
                method: form.method || 'GET',
                fields: [],
                id: form.id || `form-${index}`
            };

            // Extract form fields
            const fieldElements = form.querySelectorAll('input, textarea, select');
            fieldElements.forEach(field => {
                const fieldInfo = {
                    type: field.type || field.tagName.toLowerCase(),
                    name: field.name || '',
                    placeholder: field.placeholder || '',
                    label: '',
                    required: field.required || false
                };

                // Try to find associated label
                const label = form.querySelector(`label[for="${field.id}"]`) ||
                             field.closest('label') ||
                             field.previousElementSibling;

                if (label && label.tagName === 'LABEL') {
                    fieldInfo.label = cleanText(label.textContent || label.innerText || '');
                }

                formData.fields.push(fieldInfo);
            });

            if (formData.fields.length > 0) {
                forms.push(formData);
            }
        });

        return forms;
    }

    /**
     * Formats scraped data for insertion into note with Excel-like structure
     * @param {Object} scrapedData - The scraped data object
     * @returns {string} Formatted text for note insertion
     */
    function formatScrapedDataForNote(scrapedData) {
        let formattedContent = '';

        // Header with timestamp and URL
        formattedContent += `🕷️ WEB SCRAPER RESULTS\n`;
        formattedContent += `${'═'.repeat(50)}\n`;
        formattedContent += `📅 Scraped: ${new Date(scrapedData.timestamp).toLocaleString()}\n`;
        formattedContent += `🌐 URL: ${scrapedData.url}\n`;
        formattedContent += `⚙️ Method: ${scrapedData.extractionMethod}\n\n`;

        // Page Metadata Section
        if (scrapedData.metadata && scrapedData.metadata.title) {
            formattedContent += `📄 PAGE INFORMATION\n`;
            formattedContent += `${'─'.repeat(30)}\n`;
            formattedContent += `Title: ${scrapedData.metadata.title}\n`;
            if (scrapedData.metadata.description) {
                formattedContent += `Description: ${scrapedData.metadata.description}\n`;
            }
            if (scrapedData.metadata.author) {
                formattedContent += `Author: ${scrapedData.metadata.author}\n`;
            }
            if (scrapedData.metadata.keywords) {
                formattedContent += `Keywords: ${scrapedData.metadata.keywords}\n`;
            }
            formattedContent += `Site: ${scrapedData.metadata.siteName}\n`;
            formattedContent += `Language: ${scrapedData.metadata.language}\n\n`;
        }

        // Main Content Section
        if (scrapedData.mainContent && scrapedData.mainContent.text) {
            formattedContent += `📝 MAIN CONTENT\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            // Add headings if available
            if (scrapedData.mainContent.headings && scrapedData.mainContent.headings.length > 0) {
                formattedContent += `\n📋 HEADINGS:\n`;
                scrapedData.mainContent.headings.forEach((heading, index) => {
                    const indent = '  '.repeat(heading.level - 1);
                    formattedContent += `${indent}${index + 1}. ${heading.text}\n`;
                });
                formattedContent += '\n';
            }

            // Add main text content (truncated if too long)
            const contentText = scrapedData.mainContent.text.substring(0, 2000);
            formattedContent += `Content: ${contentText}`;
            if (scrapedData.mainContent.text.length > 2000) {
                formattedContent += `... [Content truncated - ${scrapedData.mainContent.text.length} total characters]`;
            }
            formattedContent += '\n\n';
        }

        // Shopping Items Section
        if (scrapedData.shoppingItems && scrapedData.shoppingItems.length > 0) {
            formattedContent += `🛒 SHOPPING ITEMS (${scrapedData.shoppingItems.length} found)\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            scrapedData.shoppingItems.forEach((item, index) => {
                formattedContent += `${index + 1}. ${item.title || 'Untitled Product'}\n`;
                if (item.price) formattedContent += `   💰 Price: ${item.price}\n`;
                if (item.rating) formattedContent += `   ⭐ Rating: ${item.rating}\n`;
                if (item.description) {
                    const desc = item.description.substring(0, 150);
                    formattedContent += `   📝 Description: ${desc}${item.description.length > 150 ? '...' : ''}\n`;
                }
                if (item.link) formattedContent += `   🔗 Link: ${item.link}\n`;
                formattedContent += '\n';
            });
        }

        // Contact Information Section
        if (scrapedData.contactInfo) {
            const hasContactInfo = scrapedData.contactInfo.emails.length > 0 ||
                                 scrapedData.contactInfo.phones.length > 0 ||
                                 scrapedData.contactInfo.addresses.length > 0 ||
                                 scrapedData.contactInfo.socialMedia.length > 0;

            if (hasContactInfo) {
                formattedContent += `📞 CONTACT INFORMATION\n`;
                formattedContent += `${'─'.repeat(30)}\n`;

                if (scrapedData.contactInfo.emails.length > 0) {
                    formattedContent += `📧 Emails:\n`;
                    scrapedData.contactInfo.emails.forEach(email => {
                        formattedContent += `   • ${email}\n`;
                    });
                }

                if (scrapedData.contactInfo.phones.length > 0) {
                    formattedContent += `📱 Phone Numbers:\n`;
                    scrapedData.contactInfo.phones.forEach(phone => {
                        formattedContent += `   • ${phone}\n`;
                    });
                }

                if (scrapedData.contactInfo.addresses.length > 0) {
                    formattedContent += `📍 Addresses:\n`;
                    scrapedData.contactInfo.addresses.forEach(address => {
                        formattedContent += `   • ${address}\n`;
                    });
                }

                if (scrapedData.contactInfo.socialMedia.length > 0) {
                    formattedContent += `📱 Social Media:\n`;
                    scrapedData.contactInfo.socialMedia.forEach(social => {
                        formattedContent += `   • ${social}\n`;
                    });
                }

                formattedContent += '\n';
            }
        }

        // Links Section (limited to first 10 to avoid overwhelming)
        if (scrapedData.links && scrapedData.links.length > 0) {
            formattedContent += `🔗 LINKS (${scrapedData.links.length} found, showing first 10)\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            const linksToShow = scrapedData.links.slice(0, 10);
            linksToShow.forEach((link, index) => {
                formattedContent += `${index + 1}. ${link.text}\n`;
                formattedContent += `   🌐 ${link.url}\n`;
                if (link.title) formattedContent += `   📝 ${link.title}\n`;
                formattedContent += '\n';
            });

            if (scrapedData.links.length > 10) {
                formattedContent += `   ... and ${scrapedData.links.length - 10} more links\n\n`;
            }
        }

        // Media Content Section (summary)
        if (scrapedData.media) {
            const hasMedia = scrapedData.media.images.length > 0 ||
                           scrapedData.media.videos.length > 0 ||
                           scrapedData.media.audio.length > 0;

            if (hasMedia) {
                formattedContent += `🎨 MEDIA CONTENT\n`;
                formattedContent += `${'─'.repeat(30)}\n`;

                if (scrapedData.media.images.length > 0) {
                    formattedContent += `🖼️ Images: ${scrapedData.media.images.length} found\n`;
                }
                if (scrapedData.media.videos.length > 0) {
                    formattedContent += `🎥 Videos: ${scrapedData.media.videos.length} found\n`;
                }
                if (scrapedData.media.audio.length > 0) {
                    formattedContent += `🎵 Audio: ${scrapedData.media.audio.length} found\n`;
                }
                formattedContent += '\n';
            }
        }

        // Forms Section (summary)
        if (scrapedData.forms && scrapedData.forms.length > 0) {
            formattedContent += `📋 FORMS (${scrapedData.forms.length} found)\n`;
            formattedContent += `${'─'.repeat(30)}\n`;

            scrapedData.forms.forEach((form, index) => {
                formattedContent += `${index + 1}. Form with ${form.fields.length} fields\n`;
                if (form.action) formattedContent += `   🎯 Action: ${form.action}\n`;
                formattedContent += `   📝 Method: ${form.method}\n`;

                // Show field types
                const fieldTypes = form.fields.map(field => field.type).filter((type, index, arr) => arr.indexOf(type) === index);
                formattedContent += `   🔧 Field Types: ${fieldTypes.join(', ')}\n\n`;
            });
        }

        // Footer
        formattedContent += `${'═'.repeat(50)}\n`;
        formattedContent += `✅ Web scraping completed successfully\n\n`;

        return formattedContent;
    }

    /**
     * Utility function to clean and normalize text
     * @param {string} text - Text to clean
     * @returns {string} Cleaned text
     */
    function cleanText(text) {
        if (!text) return '';

        return text
            .replace(/\s+/g, ' ')           // Replace multiple whitespace with single space
            .replace(/\n\s*\n/g, '\n')      // Replace multiple newlines with single newline
            .trim();                        // Remove leading/trailing whitespace
    }

    // Make the handler function globally available
    window.handleWebScraperNote = handleWebScraperNote;

})();

// --- END OF FILE content-web-scraper.js ---
