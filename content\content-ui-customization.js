// --- START OF FILE content-ui-customization.js ---

/**
 * UI Customization Handler for Stashy Note Interface
 * Handles applying user customization settings to the note interface
 */

// Global variable to store current UI customization settings
let uiCustomizationSettings = null;

/**
 * Initialize UI customization system
 */
function initUICustomization() {
    console.log("Stashy: Initializing UI customization system...");

    // Add CSS for hidden elements
    if (!document.getElementById('Stashy-ui-customization-styles')) {
        const style = document.createElement('style');
        style.id = 'Stashy-ui-customization-styles';
        style.textContent = `
            .Stashy-ui-hidden {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Load settings from storage
    loadUICustomizationSettings();

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'applyUICustomization') {
            uiCustomizationSettings = message.settings;
            applyUICustomization();
            sendResponse({success: true});
        }
    });
}

/**
 * Load UI customization settings from storage
 */
function loadUICustomizationSettings() {
    chrome.storage.local.get(['Stashy_ui_customization'], (result) => {
        if (result.Stashy_ui_customization) {
            uiCustomizationSettings = result.Stashy_ui_customization;
            console.log("Stashy: Loaded UI customization settings", uiCustomizationSettings);

            // Apply settings if note interface is already loaded
            if (typeof noteContainer !== 'undefined' && noteContainer) {
                applyUICustomization();
            }
        }
    });
}

// Debug function removed for performance

/**
 * Apply UI customization settings to the note interface
 */
function applyUICustomization() {
    if (!uiCustomizationSettings) return;

    // Apply customizations in order
    applyToolbarDropdownCustomization();
    applyUIElementsCustomization();
    applySnippetButtonsCustomization();
}

// Periodic retry and force apply functions removed for performance

/**
 * Apply toolbar dropdown customizations
 */
function applyToolbarDropdownCustomization() {
    const settings = uiCustomizationSettings.toolbarDropdowns;
    if (!settings) return;

    // Start looking for control wrappers from the note container
    const noteContainer = document.querySelector('#Stashy-container, .Stashy-note-container');
    if (!noteContainer) {
        console.log("Stashy: Note container not found, skipping toolbar customization");
        return;
    }

    // Look for controls wrapper directly in the note container
    let controlsWrapper = noteContainer.querySelector('.Stashy-controls-wrapper, .Stashy-toolbar-controls, .Stashy-toolbar, .Stashy-controls');

    // If not found by class, look for any element containing dropdown buttons
    if (!controlsWrapper) {
        const headerElement = noteContainer.querySelector('#Stashy-header');
        if (headerElement) {
            const dropdownsInHeader = headerElement.querySelectorAll('.Stashy-dropdown');
            if (dropdownsInHeader.length > 0) {
                // Use the parent of the first dropdown as our controls wrapper
                controlsWrapper = dropdownsInHeader[0].parentElement;

            }
        }
    }

    // Last resort - search entire note container for dropdowns
    if (!controlsWrapper) {
        const allDropdowns = noteContainer.querySelectorAll('.Stashy-dropdown');
        if (allDropdowns.length > 0) {
            // Get the common parent of all dropdowns
            const firstDropdown = allDropdowns[0];
            controlsWrapper = firstDropdown.parentElement;

        }
    }

    if (!controlsWrapper) return;

    // Get all dropdowns first to work with, but filter out note switcher
    const allDropdowns = Array.from(controlsWrapper.querySelectorAll('.Stashy-dropdown')).filter(dd => {
        const button = dd.querySelector('.Stashy-dropdown-button, button');
        if (button) {
            const text = button.textContent.toLowerCase().trim();
            // Exclude note switcher (contains "note" followed by a number/name)
            if (text.match(/note\s+\d+/) || text.includes('📑')) {
                return false;
            }
        }
        return true;
    });

    // Create a map of dropdown elements keyed by their type for easier access
    const dropdownMap = {};

    // Identify each dropdown
    allDropdowns.forEach((dd) => {
        const button = dd.querySelector('.Stashy-dropdown-button, button');
        const iconSpan = dd.querySelector('.Stashy-icon');

        if (button) {
            const text = button.textContent.toLowerCase().trim();
            let type = null;

            // Try to identify by text
            if (text.includes('format')) type = 'format-dropdown';
            else if (text.includes('style') || text.includes('appearance')) type = 'style-dropdown';
            else if (text.includes('insert')) type = 'insert-dropdown';
            else if (text.includes('tools')) type = 'tools-dropdown';
            else if (text.includes('view')) type = 'view-dropdown';

            // If not identified by text, try by icon
            if (!type && iconSpan) {
                const icon = iconSpan.textContent.trim();
                if (icon === '🎨') type = 'format-dropdown';
                else if (icon === '✨') type = 'style-dropdown';
                else if (icon === '➕') type = 'insert-dropdown';
                else if (icon === '🛠️') type = 'tools-dropdown';
                else if (icon === '👁️') type = 'view-dropdown';
            }

            // If identified, add to the map
            if (type) {
                dropdownMap[type] = dd;
            }
        }
    });

    // If no dropdowns were identified, try position-based fallback
    if (Object.keys(dropdownMap).length === 0 && allDropdowns.length > 0) {
        const dropdownTypes = ['format-dropdown', 'style-dropdown', 'insert-dropdown', 'tools-dropdown', 'view-dropdown'];
        dropdownTypes.forEach((type, index) => {
            if (index < allDropdowns.length) {
                dropdownMap[type] = allDropdowns[index];
            }
        });
    }

    // Apply visibility settings
    if (settings.visibility) {
        Object.entries(settings.visibility).forEach(([elementName, isVisible]) => {
            const dropdown = dropdownMap[elementName];
            if (dropdown) {
                if (isVisible) {
                    dropdown.style.removeProperty('display');
                    dropdown.style.removeProperty('visibility');
                    dropdown.classList.remove('Stashy-ui-hidden');
                } else {
                    dropdown.style.setProperty('display', 'none', 'important');
                    dropdown.style.setProperty('visibility', 'hidden', 'important');
                    dropdown.classList.add('Stashy-ui-hidden');
                }
            }
        });
    }

    // Apply order settings - only if we have dropdowns to reorder AND order is different
    if (settings.order && settings.order.length > 0) {
        // Get current order of dropdowns that exist in dropdownMap
        const currentOrder = [];
        const allCurrentDropdowns = Array.from(controlsWrapper.querySelectorAll('.Stashy-dropdown')).filter(dd => {
            const button = dd.querySelector('.Stashy-dropdown-button, button');
            if (button) {
                const text = button.textContent.toLowerCase().trim();
                if (text.match(/note\s+\d+/) || text.includes('📑')) {
                    return false; // Exclude note switcher
                }
            }
            return true;
        });

        // Get current order
        allCurrentDropdowns.forEach(dd => {
            for (const [type, dropdown] of Object.entries(dropdownMap)) {
                if (dropdown === dd) {
                    currentOrder.push(type);
                    break;
                }
            }
        });

        // Filter desired order to only include dropdowns that exist
        const filteredDesiredOrder = settings.order.filter(type => dropdownMap[type]);

        // Only reorder if the order is actually different
        const orderChanged = JSON.stringify(currentOrder) !== JSON.stringify(filteredDesiredOrder);

        if (orderChanged) {
            // Find the correct parent for toolbar dropdowns
            const firstDropdown = Object.values(dropdownMap)[0];
            if (firstDropdown) {
                const dropdownParent = firstDropdown.parentElement;

                // Make sure we're not accidentally affecting snippet buttons
                if (dropdownParent && !dropdownParent.classList.contains('Stashy-quick-snippets-toolbar')) {
                    // Reorder dropdowns
                    filteredDesiredOrder.forEach(elementName => {
                        const dropdown = dropdownMap[elementName];
                        if (dropdown) {
                            dropdownParent.appendChild(dropdown);
                        }
                    });
                }
            }
        }
    }
}

/**
 * Apply UI elements customizations
 */
function applyUIElementsCustomization() {
    const settings = uiCustomizationSettings.uiElements;
    if (!settings || !settings.visibility) return;

    // Apply visibility settings with special handling for each element
    Object.entries(settings.visibility).forEach(([elementName, isVisible]) => {

        switch (elementName) {
            case 'note-title':
                const noteTitle = document.getElementById('Stashy-note-title-input');
                if (noteTitle) {
                    if (isVisible) {
                        noteTitle.style.removeProperty('display');
                        noteTitle.style.removeProperty('visibility');
                        noteTitle.classList.remove('Stashy-ui-hidden');
                    } else {
                        noteTitle.style.setProperty('display', 'none', 'important');
                        noteTitle.style.setProperty('visibility', 'hidden', 'important');
                        noteTitle.classList.add('Stashy-ui-hidden');
                    }

                }
                break;

            case 'tags-input':
                // Hide the entire tags wrapper, not just the input
                const tagsInput = document.getElementById('Stashy-tags');
                if (tagsInput) {
                    const tagsWrapper = tagsInput.closest('.Stashy-input-wrapper');
                    const targetElement = tagsWrapper || tagsInput;

                    if (isVisible) {
                        targetElement.style.removeProperty('display');
                        targetElement.style.removeProperty('visibility');
                        targetElement.classList.remove('Stashy-ui-hidden');
                    } else {
                        targetElement.style.setProperty('display', 'none', 'important');
                        targetElement.style.setProperty('visibility', 'hidden', 'important');
                        targetElement.classList.add('Stashy-ui-hidden');
                    }
                }
                break;

            case 'notebook-selector':
                const notebookSelector = document.getElementById('Stashy-notebook-selector');
                if (notebookSelector) {
                    notebookSelector.style.display = isVisible ? '' : 'none';
                }
                break;

            case 'reminder-input':
                // Hide the entire reminder wrapper, not just the input
                const reminderInput = document.getElementById('Stashy-reminder');
                if (reminderInput) {
                    const reminderWrapper = reminderInput.closest('.Stashy-input-wrapper');
                    if (reminderWrapper) {
                        reminderWrapper.style.display = isVisible ? '' : 'none';
                    } else {
                        reminderInput.style.display = isVisible ? '' : 'none';
                    }
                }
                break;

            case 'note-switcher':
                const noteSwitcher = document.getElementById('Stashy-note-switcher-btn');
                if (noteSwitcher) {
                    noteSwitcher.style.display = isVisible ? '' : 'none';
                }
                break;

            case 'timestamp-display':
                const timestamp = document.getElementById('Stashy-timestamp');
                if (timestamp) {
                    timestamp.style.display = isVisible ? '' : 'none';
                }
                break;
        }
    });

    // Check if metadata container should be hidden
    const metadataContainer = document.getElementById('Stashy-metadata-container');
    if (metadataContainer) {
        const visibleWrappers = Array.from(metadataContainer.children).filter(child =>
            child.style.display !== 'none'
        );
        metadataContainer.style.display = visibleWrappers.length > 0 ? '' : 'none';
    }
}

/**
 * Apply snippet buttons customizations
 */
function applySnippetButtonsCustomization() {
    const settings = uiCustomizationSettings.snippetButtons;
    if (!settings) return;

    // Find the snippet toolbar
    const snippetToolbar = document.querySelector('.Stashy-quick-snippets-toolbar');
    if (!snippetToolbar) return;

    // Simple and fast button mapping by ID
    const buttonMap = {
        'datetime-button': document.getElementById('Stashy-snippet-datetime-btn'),
        'pageinfo-button': document.getElementById('Stashy-snippet-pageinfo-btn'),
        'highlight-button': document.getElementById('Stashy-snippet-highlight-btn'),
        'timestamp-button': document.getElementById('Stashy-snippet-timestamp-btn'),
        'webscraper-button': document.getElementById('Stashy-snippet-webscraper-btn'),
        'ai-webpage-button': document.getElementById('Stashy-snippet-ai-webpage-btn'),
        'ai-video-button': document.getElementById('Stashy-snippet-ai-video-btn'),
        'ai-note-button': document.getElementById('Stashy-snippet-ai-note-btn'),
        'ai-academic-button': document.getElementById('Stashy-snippet-ai-academic-btn'),
        'ai-search-button': document.getElementById('Stashy-snippet-ai-search-btn')
    };

    // Remove null entries
    Object.keys(buttonMap).forEach(key => {
        if (!buttonMap[key]) {
            delete buttonMap[key];
        }
    });

    // Apply visibility settings
    if (settings.visibility) {
        Object.entries(settings.visibility).forEach(([elementName, isVisible]) => {
            const button = buttonMap[elementName];
            if (button) {
                if (isVisible) {
                    button.style.removeProperty('display');
                    button.style.removeProperty('visibility');
                    button.classList.remove('Stashy-ui-hidden');
                } else {
                    button.style.setProperty('display', 'none', 'important');
                    button.style.setProperty('visibility', 'hidden', 'important');
                    button.classList.add('Stashy-ui-hidden');
                }
            }
        });
    }

    // Apply order settings if we have buttons to reorder AND order is different
    if (settings.order && settings.order.length > 0) {
        // Get current order of buttons that exist in buttonMap
        const currentOrder = [];
        const allCurrentButtons = Array.from(snippetToolbar.querySelectorAll('button'));

        allCurrentButtons.forEach(btn => {
            for (const [type, button] of Object.entries(buttonMap)) {
                if (button === btn) {
                    currentOrder.push(type);
                    break;
                }
            }
        });

        // Filter desired order to only include buttons that exist
        const filteredDesiredOrder = settings.order.filter(type => buttonMap[type]);

        // Only reorder if the order is actually different
        const orderChanged = JSON.stringify(currentOrder) !== JSON.stringify(filteredDesiredOrder);

        if (orderChanged) {
            // Reorder buttons
            filteredDesiredOrder.forEach(elementName => {
                const button = buttonMap[elementName];
                if (button) {
                    snippetToolbar.appendChild(button);
                }
            });
        }
    }
}

/**
 * Hook into the note creation process to apply customizations
 */
function hookIntoNoteCreation() {
    // Override the createNoteUI function if it exists
    if (typeof createNoteUI === 'function') {
        const originalCreateNoteUI = createNoteUI;

        window.createNoteUI = function(...args) {
            const result = originalCreateNoteUI.apply(this, args);

            // Apply customizations after UI is created with a longer delay
            setTimeout(() => {
                if (uiCustomizationSettings) {
                    applyUICustomization();
                }
            }, 500);

            return result;
        };
    }

    // Lightweight DOM observer for critical UI changes only
    let observerTimeout = null;
    const observer = new MutationObserver(() => {
        if (observerTimeout) return; // Debounce

        observerTimeout = setTimeout(() => {
            if (uiCustomizationSettings) {
                applyUICustomization();
            }
            observerTimeout = null;
        }, 500);
    });

    // Only observe the Stashy container to reduce performance impact
    const StashyContainer = document.querySelector('#Stashy-container');
    if (StashyContainer) {
        observer.observe(StashyContainer, {
            childList: true,
            subtree: false // Only direct children
        });
    }
}

// Initialize when the script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initUICustomization);
} else {
    initUICustomization();
}

// Hook into note creation
hookIntoNoteCreation();

// UI Customization script loaded

// --- END OF FILE content-ui-customization.js ---
