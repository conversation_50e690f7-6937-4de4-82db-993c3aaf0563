<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Stashy Control</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <!-- Header -->
    <h3><span class="popup-icon">📋</span> Stashy</h3>

    <!-- User Welcome Section -->
    <div id="user-welcome-section" class="user-welcome-section">
        <!-- Sign In State -->
        <div id="user-sign-in-area" class="user-auth-area">
            <button id="user-sign-in-button" class="user-auth-button" title="Sign in with Google">
                <span class="popup-icon">👤</span> Sign In
            </button>
            <p class="auth-description">Sign in to access your profile and sync features</p>
        </div>

        <!-- User Info Display (hidden by default) -->
        <div id="user-info-display" class="user-info-display hidden">
            <div class="user-profile">
                <div class="user-avatar">
                    <img id="user-avatar-img" src="" alt="User Avatar" class="avatar-img">
                    <span id="user-avatar-fallback" class="avatar-fallback">👤</span>
                </div>
                <div class="user-details">
                    <div class="user-name" id="user-name">Loading...</div>
                    <div class="user-email" id="user-email">Loading...</div>
                </div>
                <button id="user-sign-out-button" class="sign-out-button" title="Sign Out">
                    <span class="popup-icon">🚪</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Premium Status & Trial Section -->
    <div id="premium-trial-section" class="premium-trial-section">
        <!-- Premium Status Badge -->
        <div class="premium-status-container">
            <span id="premium-status" class="premium-badge free">Free</span>
            <div id="trial-countdown" class="trial-countdown hidden">
                <span class="countdown-icon">⏰</span>
                <span id="trial-days-remaining">7 days</span>
                <span class="countdown-text">trial remaining</span>
            </div>
        </div>

        <!-- Trial Activation Button -->
        <div id="trial-activation-container" class="trial-activation-container hidden">
            <button id="trial-activation-button" class="trial-button">
                <span class="trial-icon">🎉</span>
                Start 7-Day Free Trial
            </button>
            <p class="trial-description">Get unlimited access to all premium features</p>
        </div>

        <!-- Usage Counters -->
        <div id="usage-counters" class="usage-counters">
            <div class="usage-item">
                <span class="usage-label">Notes:</span>
                <span id="notes-usage" class="usage-count">0/10</span>
            </div>
            <div class="usage-item">
                <span class="usage-label">Notebooks:</span>
                <span id="notebooks-usage" class="usage-count">0/2</span>
            </div>
            <div class="usage-item">
                <span class="usage-label">Highlights:</span>
                <span id="highlights-usage" class="usage-count">0/10</span>
            </div>
        </div>
    </div>

    <!-- Global Search Area -->
    <div class="search-area">
        <input type="text" id="search-query" placeholder='Search (e.g., text tag:proj url:site)...'>
        <button id="search-button"><span class="popup-icon">🔍</span> Search</button>
    </div>

    <!-- Global Notes Section -->
    <div id="global-notes-results">
        <h3><span class="popup-icon">⭐</span> Important Notes</h3>
        <div class="results-content">
            <p class="no-results">Loading important notes...</p> <!-- Updated initial message -->
        </div>
    </div>

    <!-- Standard Search Results Area -->
    <div id="search-results">
        <p class="no-results">Enter search query above.</p>
    </div>

    <!-- Common Export Format Selection -->
    <div class="action-buttons common-export-controls">
         <label for="common-export-format-select" class="export-format-label">Export Format:</label>
         <select id="common-export-format-select" title="Select export format for notes and highlights">
             <option value="txt">Text (.txt)</option>
             <option value="md">Markdown (.md)</option>
             <option value="html">HTML (.html)</option>
             <option value="pdf">PDF (.pdf)</option>
         </select>
    </div>

    <!-- Export Action Buttons -->
    <div class="action-buttons export-actions">
        <button id="export-notes" data-premium-feature="export_pdf" title="Export Notes from Current Page"><span class="popup-icon">📄</span> Export Notes</button>
        <button id="export-highlights" data-premium-feature="export_pdf" title="Export Highlights from Current Page"><span class="popup-icon">✨</span> Export Highlights</button>
    </div>

    <!-- Web Tools Section -->
    <div class="action-buttons web-tools-actions">
        <button id="web-scraper-button" title="Extract and organize all content from current webpage"><span class="popup-icon">🕷️</span> Web Scraper</button>
    </div>

    <!-- Other Page Actions Row -->
    <div class="action-buttons other-page-actions">
        <button id="clear-note" class="danger" title="Clear Notes & Highlights on Current Page"><span class="popup-icon">🗑️</span> Clear Page Data</button>
    </div>

    <!-- Google Drive Sync Actions -->
    <div class="action-buttons sync-action">
        <button id="connect-drive-button" title="Connect to Google Drive (Sign in required)"><span class="popup-icon">🔗</span> Connect Drive</button>
        <button id="disconnect-drive-button" style="display: none;" title="Disconnect from Google Drive"><span class="popup-icon">❌</span> Disconnect</button>
        <span id="sync-status-indicator" title="Sync Status"></span>
        <p id="drive-auth-notice" class="auth-notice hidden">Please sign in first to connect Google Drive</p>
    </div>

    <!-- Dashboard Link/Button -->
    <div class="action-buttons dashboard-action">
         <button id="view-dashboard-button" title="View All Notes"><span class="popup-icon">📊</span> View Dashboard</button>
    </div>



    <!-- Settings Button with Dropdown -->
    <div class="action-buttons settings-actions">
        <!-- Main Settings Button -->
        <div class="settings-dropdown">
            <button id="main-settings-button" title="Settings"><span class="popup-icon">⚙️</span> Settings</button>
            <div id="settings-dropdown-menu" class="dropdown-menu">
                <!-- Voice Settings Option -->
                <div class="dropdown-item" id="voice-settings-option">
                    <span class="popup-icon">🎤</span> Voice Settings
                </div>

                <!-- Note Settings Option -->
                <div class="dropdown-item" id="note-settings-option">
                    <span class="popup-icon">📝</span> Note Settings
                </div>

                <!-- UI Customization Option -->
                <div class="dropdown-item" id="ui-customization-option">
                    <span class="popup-icon">🎨</span> UI Customization
                </div>

                <!-- Screenshot Settings Option -->
                <div class="dropdown-item" id="screenshot-settings-option">
                    <span class="popup-icon">📸</span> Screenshot Settings
                </div>

                <!-- AI Features Option -->
                <div class="dropdown-item" id="ai-settings-option">
                    <span class="popup-icon">🤖</span> AI Features
                </div>

                <!-- Help & Info Option -->
                <div class="dropdown-item" id="help-info-option">
                    <span class="popup-icon">❓</span> Help & Info
                </div>
            </div>
        </div>
    </div>

    <!-- Voice Settings Panel (hidden by default) -->
    <div id="voice-settings-panel" class="settings-panel" style="display: none;">
        <div class="panel-header">
            <h3><span class="popup-icon">🎤</span> Voice Transcription Settings</h3>
            <button id="close-voice-settings" class="close-button" title="Close Settings">×</button>
        </div>
        <div class="settings-container">
            <!-- Provider Section -->
            <div class="settings-section">
                <h4>Speech Recognition Provider</h4>
                <div class="form-group">
                    <label for="voice-provider">Select Provider:</label>
                    <select id="voice-provider">
                        <option value="browser">Browser Speech API (Default)</option>
                        <option value="google">Google Speech-to-Text</option>
                        <option value="azure">Azure Speech Service</option>
                        <option value="assembly">AssemblyAI</option>
                    </select>
                </div>

                <div id="api-key-container" class="form-group hidden">
                    <label for="voice-api-key">API Key (You provide your own):</label>
                    <div class="api-key-input-container">
                        <input type="password" id="voice-api-key" placeholder="Enter your API key">
                        <button id="toggle-password" class="toggle-password" title="Show/Hide Password">
                            <span class="popup-icon">👁️</span>
                        </button>
                    </div>
                    <p class="setting-description">Your API key is stored securely in your browser and never shared. Get your API key from the provider's website.</p>
                </div>

                <!-- Provider Info Text (Example structure, text filled by JS) -->
                 <div id="provider-info" class="info-text">
                    <strong>Browser Speech API (Default)</strong><br>
                    Uses your browser's built-in speech recognition. No API key required. Quality varies by browser and language support may be limited.
                </div>
            </div>

            <!-- Language Section -->
            <div class="settings-section">
                <h4>Language Settings</h4>
                <div class="form-group">
                    <label for="voice-language">Recognition Language:</label>
                    <select id="voice-language">
                        <!-- Populated by JavaScript -->
                        <option>Loading languages...</option>
                    </select>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-auto-detect">
                    <label for="voice-auto-detect">Auto-detect language</label>
                </div>
            </div>

            <!-- Options Section -->
            <div class="settings-section">
                <h4>Transcription Options</h4>
                <div class="checkbox-group">
                    <input type="checkbox" id="voice-punctuation" checked>
                    <label for="voice-punctuation">Automatic punctuation</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-capitalization" checked>
                    <label for="voice-capitalization">Automatic capitalization</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-profanity-filter">
                    <label for="voice-profanity-filter">Filter profanity</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-interim-results" checked>
                    <label for="voice-interim-results">Show interim results</label>
                </div>

                <div class="form-group">
                    <label for="voice-silence-threshold">Stop after silence:</label>
                    <div class="slider-container">
                        <input type="range" min="5" max="60" value="15" class="slider" id="voice-silence-threshold">
                        <span id="silence-value">15 seconds</span>
                    </div>
                </div>
            </div>

            <!-- Advanced Section -->
            <div class="settings-section">
                <h4>Advanced Features (Provider Dependent)</h4>
                <div class="form-group">
                    <label for="voice-quality">Transcription Quality:</label>
                    <select id="voice-quality">
                        <option value="standard">Standard</option>
                        <option value="enhanced">Enhanced</option>
                        <option value="high">High</option>
                        <option value="ultra">Ultra (Highest accuracy)</option>
                    </select>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-background-noise-reduction">
                    <label for="voice-background-noise-reduction">Background noise reduction</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="voice-speaker-diarization">
                    <label for="voice-speaker-diarization">Speaker identification</label>
                </div>
            </div>

            <!-- Status Message for Voice Settings -->
            <div id="status-message" class="status-message" role="status" aria-live="polite">
                <span id="status-text"></span>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button id="reset-settings" class="secondary-button">Reset to Defaults</button>
                <button id="save-voice-settings" class="primary-button">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Note Settings Panel (hidden by default) -->
    <div id="note-settings-panel" class="settings-panel hidden">
        <div class="panel-header">
            <h3><span class="popup-icon">⚙️</span> Note Settings</h3>
            <button id="close-note-settings" class="close-button" title="Close Settings">×</button>
        </div>
        <div class="settings-container">


            <!-- Default Note Size Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">📏</span> Default Note Size</h4>
                <p class="setting-description">Set the default size for new notes.</p>
                <div class="form-group">
                    <label for="default-width">
                        <span class="settings-icon">↔️</span> Width:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-width" min="200" max="800" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="default-height">
                        <span class="settings-icon">↕️</span> Height:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-height" min="150" max="800" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
            </div>

            <!-- Default Note Position Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">📍</span> Default Note Position</h4>
                <p class="setting-description">Set where new notes appear on the page.</p>
                <div class="form-group">
                    <label for="default-top">
                        <span class="settings-icon">⬆️</span> Top:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-top" min="0" max="500" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="default-right">
                        <span class="settings-icon">⬅️</span> Right:
                    </label>
                    <div class="input-with-unit">
                        <input type="number" id="default-right" min="0" max="500" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
            </div>



            <!-- Status Message for Note Settings -->
            <div id="note-status-message" class="status-message" role="status" aria-live="polite">
                <span id="note-status-text"></span>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button id="reset-note-settings" class="secondary-button">
                    Reset to Defaults
                </button>
                <button id="save-note-settings" class="primary-button">
                    Save Settings
                </button>
            </div>
        </div>
    </div>



    <!-- Screenshot Settings Panel (hidden by default) -->
    <div id="screenshot-settings-panel" class="settings-panel hidden">
        <div class="panel-header">
            <h3><span class="popup-icon">📸</span> Screenshot Settings</h3>
            <button id="close-screenshot-settings" class="close-button" title="Close Settings">×</button>
        </div>
        <div class="settings-container">
            <!-- Storage Location Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">💾</span> Storage Location</h4>
                <div class="form-group storage-location-group">
                    <label>Where to save screenshots:</label>
                    <div class="storage-options">
                        <div class="storage-option">
                            <input type="radio" id="storage-local" name="storage-location" value="local">
                            <label for="storage-local">
                                <span class="storage-icon">💻</span>
                                <span class="storage-label">Local Only</span>
                                <span class="storage-description">Save to your computer only</span>
                            </label>
                        </div>
                        <div class="storage-option">
                            <input type="radio" id="storage-drive" name="storage-location" value="drive">
                            <label for="storage-drive">
                                <span class="storage-icon">☁️</span>
                                <span class="storage-label">Google Drive Only</span>
                                <span class="storage-description">Save to Google Drive only</span>
                            </label>
                        </div>
                        <div class="storage-option">
                            <input type="radio" id="storage-both" name="storage-location" value="both">
                            <label for="storage-both">
                                <span class="storage-icon">🔄</span>
                                <span class="storage-label">Both</span>
                                <span class="storage-description">Save to both computer and Drive</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Drive Settings Section -->
            <div class="settings-section" id="drive-settings-section">
                <h4><span class="settings-icon">☁️</span> Google Drive Settings</h4>
                <div class="form-group">
                    <label for="drive-folder-name">Folder Name:</label>
                    <input type="text" id="drive-folder-name" placeholder="Stashy Screenshots">
                    <p class="setting-description">Name of the folder where screenshots will be saved in Google Drive.</p>
                </div>

                <div class="form-group privacy-setting">
                    <label>Privacy Setting:</label>
                    <div class="privacy-options">
                        <div class="privacy-option">
                            <input type="radio" id="privacy-private" name="privacy-setting" value="private">
                            <label for="privacy-private">
                                <span class="privacy-icon">🔒</span>
                                <span class="privacy-label">Private</span>
                                <span class="privacy-description">Only visible to this extension</span>
                            </label>
                        </div>
                        <div class="privacy-option">
                            <input type="radio" id="privacy-visible" name="privacy-setting" value="visible">
                            <label for="privacy-visible">
                                <span class="privacy-icon">👁️</span>
                                <span class="privacy-label">Visible</span>
                                <span class="privacy-description">Visible in your Google Drive</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="auto-delete-after">Auto-Delete After:</label>
                    <div class="auto-delete-container">
                        <input type="range" id="auto-delete-after" min="0" max="90" step="1" value="0">
                        <div class="auto-delete-value">
                            <span id="auto-delete-display">Never</span>
                        </div>
                    </div>
                    <div class="auto-delete-timeline" id="auto-delete-timeline">
                        <div class="timeline-now">Now</div>
                        <div class="timeline-bar">
                            <div class="timeline-marker"></div>
                        </div>
                        <div class="timeline-deletion">Deletion</div>
                    </div>
                    <p class="setting-description">Screenshots will be automatically deleted from Google Drive after the specified period. Set to 0 to keep them forever.</p>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="ask-before-upload" checked>
                    <label for="ask-before-upload">Ask for confirmation before uploading to Google Drive</label>
                    <p class="setting-description">When enabled, you'll be asked to confirm before each screenshot is uploaded to Google Drive.</p>
                </div>
            </div>

            <!-- Image Quality Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">✨</span> Image Quality</h4>
                <div class="form-group">
                    <label for="image-quality">Screenshot Quality:</label>
                    <select id="image-quality">
                        <option value="standard">Standard (Good balance of quality and size)</option>
                        <option value="high">High (Better quality, larger file size)</option>
                        <option value="maximum">Maximum (Best quality, largest file size)</option>
                    </select>
                    <p class="setting-description">Higher quality settings produce better images but result in larger file sizes.</p>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="enable-high-dpi" checked>
                    <label for="enable-high-dpi">Enable high-DPI support for retina/high-resolution displays</label>
                    <p class="setting-description">Creates sharper screenshots on high-resolution displays. May increase file size.</p>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="enable-anti-aliasing" checked>
                    <label for="enable-anti-aliasing">Enable anti-aliasing for smoother lines and text</label>
                    <p class="setting-description">Improves the appearance of text and diagonal lines in screenshots.</p>
                </div>


            </div>

            <!-- Status Message for Screenshot Settings -->
            <div id="screenshot-status-message" class="status-message" role="status" aria-live="polite">
                <span id="screenshot-status-text"></span>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button id="reset-screenshot-settings" class="secondary-button">
                    Reset to Defaults
                </button>
                <button id="save-screenshot-settings" class="primary-button">
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- AI Features Panel (hidden by default) -->
    <div id="ai-settings-panel" class="settings-panel hidden">
        <div class="panel-header">
            <h3><span class="popup-icon">🤖</span> AI Features</h3>
            <button id="close-ai-settings" class="close-button" title="Close AI Settings">×</button>
        </div>
        <div class="settings-container">
            <!-- AI Authentication Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">🔑</span> AI Authentication</h4>
                <div class="ai-auth-container">
                    <!-- Universal AI API Key Configuration -->
                    <div id="universal-ai-config" class="auth-status-container">
                        <div class="ai-provider-info">
                            <div class="api-provider-header">
                                <span class="provider-icon" id="detected-provider-icon">🤖</span>
                                <span class="provider-name" id="detected-provider-name">AI Provider</span>
                                <span class="provider-status" id="ai-provider-status">Not configured</span>
                            </div>
                            <p class="setting-description">Enter your AI API key from any supported provider. Stashy will automatically detect and configure the service.</p>
                        </div>

                        <div class="form-group">
                            <label for="universal-ai-api-key">AI API Key:</label>
                            <div class="api-key-input-container">
                                <input type="password" id="universal-ai-api-key" placeholder="Paste your API key from OpenAI, Anthropic, Google AI, or other providers" class="api-key-input">
                                <button type="button" id="toggle-universal-ai-key-visibility" class="toggle-visibility-btn" title="Show/Hide API Key">👁️</button>
                            </div>
                            <p class="setting-description">Your API key is stored securely and only used for AI requests. Supported providers are automatically detected.</p>

                            <!-- Detection Progress -->
                            <div id="detection-progress" class="detection-progress" style="display: none;">
                                <div class="progress-bar">
                                    <div class="progress-fill"></div>
                                </div>
                                <span class="progress-text">Detecting AI provider...</span>
                            </div>

                            <!-- Provider Detection Results -->
                            <div id="provider-detection-results" class="provider-detection-results" style="display: none;">
                                <div class="detection-result">
                                    <span class="detection-icon">✅</span>
                                    <span class="detection-text">Provider detected: <strong id="detected-provider-display"></strong></span>
                                </div>
                                <div class="validation-result" id="validation-result" style="display: none;">
                                    <span class="validation-icon">🔄</span>
                                    <span class="validation-text">Validating API key...</span>
                                </div>
                            </div>

                            <!-- Provider Configuration -->
                            <div id="provider-configuration" style="display: none; margin-top: 15px; padding: 12px; background-color: #f8f9fa; border-radius: 4px; border: 1px solid #dee2e6;">
                                <h4 style="margin: 0 0 15px 0; color: #495057;">⚙️ Provider Settings</h4>

                                <!-- Model Selection -->
                                <div class="form-group" style="margin-bottom: 15px;">
                                    <label for="provider-model-select" style="display: block; margin-bottom: 5px; font-weight: bold;">Model:</label>
                                    <select id="provider-model-select" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; background-color: white;">
                                        <option value="">Select a model...</option>
                                    </select>
                                </div>

                                <!-- Temperature Control -->
                                <div class="form-group" style="margin-bottom: 15px;">
                                    <label for="provider-temperature" style="display: block; margin-bottom: 5px; font-weight: bold;">Temperature: <span id="temperature-value">0.7</span></label>
                                    <input type="range" id="provider-temperature" min="0" max="1" step="0.1" value="0.7" style="width: 100%;">
                                    <div style="font-size: 12px; color: #6c757d; margin-top: 2px;">Lower = more focused, Higher = more creative</div>
                                </div>

                                <!-- Max Tokens -->
                                <div class="form-group" style="margin-bottom: 15px;">
                                    <label for="provider-max-tokens" style="display: block; margin-bottom: 5px; font-weight: bold;">Max Tokens:</label>
                                    <input type="number" id="provider-max-tokens" min="10" max="4000" value="1000" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                                    <div style="font-size: 12px; color: #6c757d; margin-top: 2px;">Maximum response length (10-4000)</div>
                                </div>

                                <!-- Save Configuration Button -->
                                <button type="button" id="save-provider-config" class="btn btn-secondary" style="background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; width: 100%;">
                                    💾 Save Configuration
                                </button>
                            </div>

                            <!-- Help Information -->
                            <div id="api-key-help" class="api-key-help" style="display: none;">
                                <strong>Supported AI Providers:</strong><br>
                                • <strong>OpenAI (ChatGPT):</strong> Get your key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a><br>
                                • <strong>Anthropic (Claude):</strong> Get your key from <a href="https://console.anthropic.com/" target="_blank">Anthropic Console</a><br>
                                • <strong>Google AI (Gemini):</strong> Get your key from <a href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</a><br>
                                • <strong>Cohere:</strong> Get your key from <a href="https://dashboard.cohere.ai/api-keys" target="_blank">Cohere Dashboard</a><br>
                                • <strong>Hugging Face:</strong> Get your token from <a href="https://huggingface.co/settings/tokens" target="_blank">HF Settings</a><br>
                                <br>
                                <strong>Want to disable AI?</strong> Click "🗑️ Clear & Disable" to remove your API key and disable all AI features.
                            </div>
                        </div>

                        <div id="ai-status-indicator" class="status-indicator">
                            <span class="status-icon">⚙️</span>
                            <span class="status-text">Configure your API key to enable AI features</span>
                        </div>

                        <!-- API Key Management -->
                        <div class="form-group" style="margin-top: 15px;">
                            <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">
                                <button type="button" id="detect-and-save-button" class="btn btn-primary" style="background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                                    🔍 Detect & Save
                                </button>
                                <button type="button" id="manual-save-button" class="btn btn-secondary" style="background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                                    💾 Save Only
                                </button>
                                <button type="button" id="test-ai-button" class="btn btn-secondary" style="background-color: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                                    🧪 Test Connection
                                </button>
                                <button type="button" id="clear-api-key-button" class="btn btn-danger" style="background-color: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                                    🗑️ Clear & Disable
                                </button>
                            </div>
                            <div id="ai-test-result" style="margin-top: 10px; padding: 8px; border-radius: 4px; display: none;"></div>
                        </div>

                        <!-- AI Features Information -->
                        <div class="ai-features-info">
                            <h5><span class="settings-icon">📚</span> How Stashy's AI Features Work</h5>

                            <div class="info-section">
                                <h6>🔐 API Key Setup</h6>
                                <p>Stashy supports multiple AI providers. Choose the one that works best for you:</p>
                                <ul class="feature-benefits">
                                    <li><strong>Google AI:</strong> <a href="https://aistudio.google.com/app/apikey" target="_blank">Get API key</a> - Generous free tier</li>
                                    <li><strong>OpenAI:</strong> <a href="https://platform.openai.com/api-keys" target="_blank">Get API key</a> - Popular and reliable</li>
                                    <li><strong>Anthropic:</strong> <a href="https://console.anthropic.com/" target="_blank">Get API key</a> - Safety-focused AI</li>
                                    <li><strong>Cohere:</strong> <a href="https://dashboard.cohere.ai/api-keys" target="_blank">Get API key</a> - Enterprise-grade</li>
                                    <li><strong>Hugging Face:</strong> <a href="https://huggingface.co/settings/tokens" target="_blank">Get token</a> - Open-source models</li>
                                </ul>
                                <p>Your API key is stored securely in your browser and only used for AI requests.</p>
                            </div>

                            <div class="info-section">
                                <h6>💰 Usage & Billing</h6>
                                <p>Most AI providers offer generous free tiers for personal use. You control your usage and billing directly through your chosen provider's account.</p>
                                <ul class="feature-benefits">
                                    <li>Free tiers available for all supported providers</li>
                                    <li>Pay-as-you-go pricing only if you exceed free limits</li>
                                    <li>Direct billing through your provider account</li>
                                    <li>No hidden fees or subscription costs from Stashy</li>
                                    <li>Switch between providers anytime</li>
                                </ul>
                            </div>

                            <div class="info-section">
                                <h6>⚡ Available AI Features</h6>
                                <ul class="feature-benefits">
                                    <li><strong>Smart Summaries:</strong> Generate concise summaries of web pages and videos</li>
                                    <li><strong>Content Analysis:</strong> Extract key insights and main points</li>
                                    <li><strong>Note Enhancement:</strong> Improve and organize your existing notes</li>
                                    <li><strong>Quick Search:</strong> AI-powered search across your notes and content</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>







            <!-- Status Message for AI Settings -->
            <div id="ai-status-message" class="status-message" role="status" aria-live="polite">
                <span id="ai-status-text"></span>
            </div>


        </div>
    </div>

    <!-- UI Customization Panel (hidden by default) -->
    <div id="ui-customization-panel" class="settings-panel hidden">
        <div class="panel-header">
            <h3><span class="popup-icon">🎨</span> UI Customization</h3>
            <button id="close-ui-customization" class="close-button" title="Close Settings">×</button>
        </div>
        <div class="settings-container">
            <!-- Toolbar Dropdowns Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">🔧</span> Toolbar Dropdowns</h4>
                <p class="setting-description">Show/hide and reorder toolbar dropdown menus (5 items - Reorderable).</p>
                <div class="customization-list" id="toolbar-dropdowns-list">
                    <div class="customization-item" data-element="format-dropdown">
                        <div class="item-controls">
                            <span class="item-icon">📝</span>
                            <label for="format-dropdown-toggle">Format</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="format-dropdown-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="style-dropdown">
                        <div class="item-controls">
                            <span class="item-icon">🎨</span>
                            <label for="style-dropdown-toggle">Style</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="style-dropdown-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="insert-dropdown">
                        <div class="item-controls">
                            <span class="item-icon">➕</span>
                            <label for="insert-dropdown-toggle">Insert</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="insert-dropdown-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="tools-dropdown">
                        <div class="item-controls">
                            <span class="item-icon">🔧</span>
                            <label for="tools-dropdown-toggle">Tools</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="tools-dropdown-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="view-dropdown">
                        <div class="item-controls">
                            <span class="item-icon">👁️</span>
                            <label for="view-dropdown-toggle">View</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="view-dropdown-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                </div>
            </div>

            <!-- UI Elements Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">👁️</span> UI Elements</h4>
                <p class="setting-description">Show/hide note interface elements (6 items - Visibility Only).</p>
                <div class="customization-list" id="ui-elements-list">
                    <div class="customization-item" data-element="note-title">
                        <div class="item-controls">
                            <span class="item-icon">📝</span>
                            <label for="note-title-toggle">Note Title Input</label>
                            <input type="checkbox" id="note-title-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="tags-input">
                        <div class="item-controls">
                            <span class="item-icon">🏷️</span>
                            <label for="tags-input-toggle">Tags Input</label>
                            <input type="checkbox" id="tags-input-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="notebook-selector">
                        <div class="item-controls">
                            <span class="item-icon">📚</span>
                            <label for="notebook-selector-toggle">Notebook Selector</label>
                            <input type="checkbox" id="notebook-selector-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="reminder-input">
                        <div class="item-controls">
                            <span class="item-icon">⏰</span>
                            <label for="reminder-input-toggle">Reminder Input</label>
                            <input type="checkbox" id="reminder-input-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="note-switcher">
                        <div class="item-controls">
                            <span class="item-icon">📑</span>
                            <label for="note-switcher-toggle">Note Switcher</label>
                            <input type="checkbox" id="note-switcher-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="timestamp-display">
                        <div class="item-controls">
                            <span class="item-icon">🕐</span>
                            <label for="timestamp-display-toggle">Timestamp Display</label>
                            <input type="checkbox" id="timestamp-display-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Snippet Buttons Section -->
            <div class="settings-section">
                <h4><span class="settings-icon">📅</span> Snippet Buttons</h4>
                <p class="setting-description">Show/hide and reorder snippet buttons (10 items - Reorderable).</p>
                <div class="customization-list" id="snippet-buttons-list">
                    <div class="customization-item" data-element="datetime-button">
                        <div class="item-controls">
                            <span class="item-icon">📅</span>
                            <label for="datetime-button-toggle">Date/Time Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="datetime-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="pageinfo-button">
                        <div class="item-controls">
                            <span class="item-icon">📄</span>
                            <label for="pageinfo-button-toggle">Page Info Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="pageinfo-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="highlight-button">
                        <div class="item-controls">
                            <span class="item-icon">🔆</span>
                            <label for="highlight-button-toggle">Highlight Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="highlight-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="timestamp-button">
                        <div class="item-controls">
                            <span class="item-icon">⏰</span>
                            <label for="timestamp-button-toggle">Video Timestamp Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="timestamp-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="webscraper-button">
                        <div class="item-controls">
                            <span class="item-icon">🕷️</span>
                            <label for="webscraper-button-toggle">Web Scraper Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="webscraper-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="ai-webpage-button">
                        <div class="item-controls">
                            <span class="item-icon">🌐</span>
                            <label for="ai-webpage-button-toggle">Web Page AI Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="ai-webpage-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="ai-video-button">
                        <div class="item-controls">
                            <span class="item-icon">📹</span>
                            <label for="ai-video-button-toggle">Video AI Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="ai-video-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="ai-note-button">
                        <div class="item-controls">
                            <span class="item-icon">📝</span>
                            <label for="ai-note-button-toggle">Note AI Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="ai-note-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="ai-academic-button">
                        <div class="item-controls">
                            <span class="item-icon">🎓</span>
                            <label for="ai-academic-button-toggle">Academic Problem Solver Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="ai-academic-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                    <div class="customization-item" data-element="ai-search-button">
                        <div class="item-controls">
                            <span class="item-icon">🔍</span>
                            <label for="ai-search-button-toggle">AI Search Button</label>
                        </div>
                        <div class="reorder-controls">
                            <span class="drag-handle" title="Drag to reorder">≡</span>
                            <button class="reorder-btn" data-direction="up" title="Move up">↑</button>
                            <button class="reorder-btn" data-direction="down" title="Move down">↓</button>
                            <input type="checkbox" id="ai-search-button-toggle" class="ui-checkbox" checked>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Message for UI Customization -->
            <div id="ui-customization-status-message" class="status-message" role="status" aria-live="polite">
                <span id="ui-customization-status-text"></span>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <button id="reset-ui-customization" class="secondary-button">
                    Reset to Defaults
                </button>
                <button id="save-ui-customization" class="primary-button">
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Help & Info Panel (hidden by default) -->
    <div id="help-info-panel" class="settings-panel hidden">
        <div class="panel-header">
            <h3><span class="popup-icon">❓</span> Help & Information</h3>
            <button id="close-help-info" class="close-button" title="Close Help & Info">×</button>
        </div>
        <div class="panel-content">
            <div class="help-links">
                <a href="help.html" target="_blank" class="help-link" title="View Help Guide">
                    <span class="popup-icon">❓</span> Help Guide
                </a>
                <a href="privacy-policy.html" target="_blank" class="help-link" title="View Privacy Policy">
                    <span class="popup-icon">🔒</span> Privacy Policy
                </a>
                <a href="permissions-explanation.html" target="_blank" class="help-link" title="View Permissions Explanation">
                    <span class="popup-icon">🔑</span> Permissions
                </a>
                <a href="data-deletion.html" target="_blank" class="help-link" title="View Data Deletion Policy">
                    <span class="popup-icon">🗑️</span> Data Deletion
                </a>
            </div>
        </div>
    </div>

    <!-- Main status message for popup -->
    <div id="main-status-message" class="status-message" role="status" aria-live="polite"></div>

    <!-- Local Security Libraries -->
    <script src="lib/pako.min.js"></script>
    <script src="lib/lz-string-local.js"></script>
    <script src="lib/url-utils.js"></script>
    <script src="lib/favicon-utils.js"></script>
    <script src="lib/security-audit.js"></script>

    <!-- Encryption Libraries -->
    <script src="lib/data-encryption.js"></script>
    <script src="lib/secure-api-storage.js"></script>
    <script src="lib/ai-security-manager.js"></script>

    <!-- AI Management Libraries -->
    <script src="lib/ai-provider-detector.js"></script>
    <script src="lib/universal-ai-adapter.js"></script>
    <script src="lib/ai-migration-bridge.js"></script>
    <script src="lib/google-ai-integration.js"></script>

    <!-- PDF Libraries -->
    <script src="lib/jspdf.umd.min.js"></script>
    <!-- Settings Script -->
    <script src="settings.js"></script>
    <!-- Documentation Links Script -->
    <script src="popup-links.js"></script>
    <!-- Main Popup Script -->
    <script src="popup.js"></script>
</body>
</html>