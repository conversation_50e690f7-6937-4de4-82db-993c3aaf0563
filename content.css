/* --- START OF FILE content.css --- */

:root {
  /* Base Colors for Notes - Light Mode */
  --Stashy-bg-yellow: #f9f9b8; /* Slightly softer yellow */
  --Stashy-header-yellow: #fdf98a; /* Lighter header */
  --Stashy-bg-blue: #c6defe;   /* Lighter blue */
  --Stashy-header-blue: #97c3f8;
  --Stashy-bg-green: #d4fce0;  /* Lighter green */
  --Stashy-header-green: #75f872;
  --Stashy-bg-pink: #faabf3;   /* Lighter pink */
  --Stashy-header-pink: #fc95ed;
  --Stashy-bg-purple: #bbaefc; /* Lighter purple */
  --Stashy-header-purple: #a793fd;

  /* New Theme Colors - Light Mode */
  --Stashy-bg-orange: #ffe0b2; /* Soft orange */
  --Stashy-header-orange: #ffcc80; /* Light orange header */
  --Stashy-bg-teal: #b2dfdb; /* Soft teal */
  --Stashy-header-teal: #80cbc4; /* Light teal header */
  --Stashy-bg-red: #ffcdd2; /* Soft red */
  --Stashy-header-red: #ef9a9a; /* Light red header */
  --Stashy-bg-brown: #d7ccc8; /* Soft brown */
  --Stashy-header-brown: #bcaaa4; /* Light brown header */
  --Stashy-bg-gray: #e0e0e0; /* Soft gray */
  --Stashy-header-gray: #bdbdbd; /* Light gray header */;

  /* Text Colors - Light Mode */
  --Stashy-text-color: #1f2937; /* Darker gray */
  --Stashy-text-light: #6b7280; /* Medium gray */


  --Stashy-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --Stashy-save-flash: #35e63e;
  --Stashy-reminder-flash: #fef08a;
  --Stashy-accent: #7ff362; /* Slightly different green */
  --Stashy-accent-darker: #149d05;
  --Stashy-border: #e5e7eb; /* Lighter border */
  --Stashy-success: #2f9503; /* Green */
  --Stashy-error: #ef4444;  /* Red */

  --Stashy-timestamp-color: #0a58ca; /* Link blue */
  --Stashy-timestamp-hover-bg: #e0f2fe;

  /* Button variables */
  --btn-bg-light: #ffffff;
  --btn-bg-dark-active: #374151; /* Darker gray for some active states */
  --btn-bg-hover: #f3f4f6;      /* Light gray for hover/light active */
  --btn-shadow-light: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --btn-shadow-heavy: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --transition-speed: 0.15s;
  --border-radius-sm: 4px;  /* Added for consistency */
  --border-radius-md: 6px; /* Consistent radius */

  /* URL and Global Note Button Colors */
  --url-note-active: #3b82f6; /* Blue for URL note */
  --global-note-active: #10b981; /* Green for global note */

  /* Light Background Colors for Dropdown Triggers */
  --Stashy-btn-bg-format: #f8f4cb; /* Lighter Yellow */
  --Stashy-btn-bg-style: #c1f9fc;  /* Lighter Cyan */
  --Stashy-btn-bg-insert: #c1f9d2; /* Lighter Green */
  --Stashy-btn-bg-tools: #d3ccf7;  /* Lighter Purple */
  --Stashy-btn-bg-view: #fff7ed;   /* Lighter Orange */
  --Stashy-btn-bg-switcher: #cae3fc;/* Lighter Gray */

  /* Active Backgrounds */
  --light-grey-active: #e5e7eb;  /* Light grey for Format/Opacity active */
  --light-green-active: #c2f5d4; /* Light green for Note Switcher active */
  --darker-text-on-light: #111827; /* Dark text for light active buttons */

  /* Highlighting Color Variables */
  --Stashy-highlight-yellow: #fbf09d; /* Adjusted */
  --Stashy-highlight-pink: #fbadd9;   /* Adjusted */
  --Stashy-highlight-blue: #a3c9fb;  /* Adjusted */
  --Stashy-highlight-green: #a2f8c0; /* Adjusted */
  --Stashy-highlight-purple: #bbacfd; /* Adjusted */

  /* NEW: Header Title Input */
  --header-input-bg: transparent;
  --header-input-bg-focus: rgba(255, 255, 255, 0.2);
  --header-input-border-focus: var(--Stashy-accent);
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
   font-family: var(--Stashy-font-family);
}

/* ===================================== */
/*         Highlighting Styles           */
/* ===================================== */

/* --- Styles for Highlights on the Page --- */
mark.Stashy-highlight {
  background-color: var(--Stashy-highlight-yellow); /* Default */
  color: inherit;
  padding: 0.1em 0.2em; /* Slightly more horizontal padding */
  cursor: default;
  /* Removed transition to prevent blinking when notes are added/removed */
  border-radius: 3px;
  box-shadow: 0 0 0 1px rgba(0,0,0,0.05); /* Softer shadow */
  position: relative; /* Needed for delete button */
}
mark.Stashy-highlight:hover:not(.has-linked-note) {
    filter: brightness(97%); /* Subtle brightness */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Softer hover shadow */
    transition: filter 0.2s ease, box-shadow 0.2s ease; /* Only apply transition when no note */
}

/* Disable hover effects for highlights with linked notes to prevent blinking */
mark.Stashy-highlight.has-linked-note:hover {
    background-color: inherit; /* Maintain current background color */
}
mark.Stashy-highlight.color-yellow { background-color: var(--Stashy-highlight-yellow); }
mark.Stashy-highlight.color-pink { background-color: var(--Stashy-highlight-pink); }
mark.Stashy-highlight.color-blue { background-color: var(--Stashy-highlight-blue); }
mark.Stashy-highlight.color-green { background-color: var(--Stashy-highlight-green); }
mark.Stashy-highlight.color-purple { background-color: var(--Stashy-highlight-purple); }

/* --- NEW Highlight Style Classes --- */
mark.Stashy-highlight.style-underline {
    text-decoration: underline;
    text-decoration-skip-ink: none; /* Ensure underline goes through descenders */
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important; /* Ensure text color isn't changed */
    padding: 0; /* Remove padding if no background */
    box-shadow: none !important; /* Remove shadow if no background */
}
mark.Stashy-highlight.style-wavy {
    text-decoration: underline wavy;
    text-decoration-skip-ink: none;
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important;
    padding: 0;
    box-shadow: none !important;
}
mark.Stashy-highlight.style-border-thick {
    /* Using outline for thicker border that doesn't affect layout */
    outline: 2px solid rgba(0, 0, 0, 0.5) !important; /* Added !important */
    outline-offset: 1px;
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important;
    padding: 0;
    box-shadow: none !important;
}
/* --- END NEW Highlight Style Classes --- */

/* --- START Add Strikethrough Style --- */
mark.Stashy-highlight.style-strikethrough {
    text-decoration: line-through !important; /* Added !important */
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important; /* Ensure text color isn't changed */
    padding: 0;
    box-shadow: none !important;
}

/* --- Privacy Highlight Styles --- */
mark.Stashy-highlight.style-blur {
    color: transparent !important; /* Hide the text */
    text-shadow: 0 0 8px rgba(0,0,0,0.5) !important; /* Blur effect */
    background-color: rgba(0,0,0,0.05) !important; /* Light background */
    padding: 0.1em 0.2em !important;
    border-radius: 3px !important;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.1) !important;
    user-select: none !important; /* Prevent selection */
}

/* Ensure links inside blur highlights are also blurred */
mark.Stashy-highlight.style-blur a,
mark.Stashy-highlight.style-blur a:link,
mark.Stashy-highlight.style-blur a:visited,
mark.Stashy-highlight.style-blur a:hover,
mark.Stashy-highlight.style-blur a:active {
    color: transparent !important;
    text-shadow: 0 0 8px rgba(0,0,0,0.5) !important;
    text-decoration: none !important;
    pointer-events: none !important; /* Prevent clicking */
}
/* --- END Add Strikethrough Style --- */

/* Delete button for Page highlights */
mark.Stashy-highlight .Stashy-delete-highlight-btn {
    position: absolute; top: -8px; right: -8px; width: 16px; height: 16px;
    border-radius: 50%; background-color: rgba(239, 68, 68, 0.85); color: white;
    border: 1px solid rgba(255, 255, 255, 0.9); font-size: 10px; line-height: 14px;
    text-align: center; cursor: pointer; opacity: 0;
    transition: opacity 0.2s ease, transform 0.2s ease, background-color 0.2s ease;
    z-index: 10000; transform: scale(0.8); font-weight: bold; padding: 0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.3); display: flex; align-items: center;
    justify-content: center; user-select: none;
}

/* Note button for Page highlights */
mark.Stashy-highlight .Stashy-note-highlight-btn {
    position: absolute; top: -8px; right: 12px; width: 16px; height: 16px;
    border-radius: 50%; background-color: rgba(59, 130, 246, 0.85); color: white;
    border: 1px solid rgba(255, 255, 255, 0.9); font-size: 10px; line-height: 14px;
    text-align: center; cursor: pointer; opacity: 0;
    transition: opacity 0.2s ease, transform 0.2s ease, background-color 0.2s ease;
    z-index: 10000; transform: scale(0.8); font-weight: bold; padding: 0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.3); display: flex; align-items: center;
    justify-content: center; user-select: none;
}

/* DISABLED: Button hover effects for highlights with linked notes to prevent blinking */
mark.Stashy-highlight:hover:not(.has-linked-note) .Stashy-delete-highlight-btn,
mark.Stashy-highlight:hover:not(.has-linked-note) .Stashy-note-highlight-btn {
    opacity: 1; transform: scale(1);
}

mark.Stashy-highlight:not(.has-linked-note) .Stashy-delete-highlight-btn:hover {
    background-color: rgba(220, 38, 38, 0.95); transform: scale(1.1);
}

mark.Stashy-highlight:not(.has-linked-note) .Stashy-note-highlight-btn:hover {
    background-color: rgba(37, 99, 235, 0.95); transform: scale(1.1);
}

/* Highlight with linked note indicator */
mark.Stashy-highlight.has-linked-note {
    position: relative;
    border-bottom: 2px dotted #ffd54f;
    /* Ensure no transitions when note is added to prevent blinking */
    transition: none !important;
}

/* Cross-paragraph highlight segments */
mark.Stashy-highlight.cross-paragraph-segment {
    /* Ensure consistent styling across segments */
    display: inline;
    /* Reduce visual gaps between segments */
    margin: 0;
    /* Ensure segments connect visually when possible */
    border-radius: 2px; /* Slightly smaller radius for segments */
    /* Add subtle visual indicator for cross-paragraph segments */
    position: relative;
    /* Slightly more pronounced styling for cross-paragraph segments */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* First segment of cross-paragraph highlight */
mark.Stashy-highlight.cross-paragraph-segment[data-segment-index="0"] {
    /* Only show buttons on the first segment to avoid clutter */
    position: relative;
}

/* Hide buttons on non-first segments */
mark.Stashy-highlight.cross-paragraph-segment:not([data-segment-index="0"]) .Stashy-delete-highlight-btn,
mark.Stashy-highlight.cross-paragraph-segment:not([data-segment-index="0"]) .Stashy-note-highlight-btn {
    display: none !important;
}

/* Visual connection indicator for cross-paragraph highlights - DISABLED */
/* Removed the visual connection indicator as it was creating unwanted lines */
mark.Stashy-highlight.cross-paragraph-segment::before {
    display: none;
}

/* Hide connection indicator on first segment */
mark.Stashy-highlight.cross-paragraph-segment[data-segment-index="0"]::before {
    display: none;
}

/* Hover effect for all segments of the same highlight */
mark.Stashy-highlight.cross-paragraph-segment:hover {
    /* Slightly more pronounced hover for segments */
    filter: brightness(95%);
    /* Add subtle border to indicate it's part of a cross-paragraph highlight */
    border: 1px solid rgba(255, 213, 79, 0.6);
    border-radius: 3px;
}

/* Add a subtle visual indicator for cross-paragraph segments */
mark.Stashy-highlight.cross-paragraph-segment:not([data-segment-index="0"])::after {
    content: '⋯';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: rgba(255, 213, 79, 0.8);
    opacity: 0.7;
    pointer-events: none;
    z-index: 1;
}

/* When hovering over any segment, highlight all segments with the same ID */
mark.Stashy-highlight.cross-paragraph-segment:hover ~ mark.Stashy-highlight[data-highlight-id],
mark.Stashy-highlight.cross-paragraph-segment:hover + mark.Stashy-highlight[data-highlight-id] {
    filter: brightness(95%);
    border: 1px solid rgba(255, 213, 79, 0.6);
    border-radius: 3px;
}

mark.Stashy-highlight.has-linked-note::after {
    content: "📝";
    position: absolute;
    top: -15px;
    right: -5px;
    font-size: 14px;
    opacity: 0;
    pointer-events: none;
    z-index: 999;
    /* Removed transitions to prevent blinking */
    transform: scale(0.8);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Disabled hover effect for note emoji to prevent blinking */
mark.Stashy-highlight.has-linked-note:hover::after {
    opacity: inherit; /* Maintain current opacity */
    transform: none; /* No transform to prevent blinking */
}

/* Note display on highlight */
.Stashy-highlight-note-display {
    position: absolute;
    top: 100%;
    left: 0;
    width: max-content;
    max-width: 300px;
    background-color: #fff9c4;
    border: 1px solid #ffd54f;
    border-radius: 8px;
    padding: 12px 16px;
    margin-top: 8px;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 2px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
    font-family: var(--Stashy-font-family);
    /* Removed transition to prevent conflicts during drag */
    cursor: move; /* Show move cursor to indicate draggable */

    /* Performance optimizations - removed contain to prevent positioning issues */
    backface-visibility: hidden; /* Prevent flickering in some browsers */

    /* Add emoji to the top-right corner */
    &::after {
        content: '📝';
        position: absolute;
        top: -10px;
        right: 8px;
        font-size: 16px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        z-index: 10000;
    }

    /* Add a subtle pointer/arrow at the top */
    &::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 15px;
        width: 14px;
        height: 14px;
        background-color: #fff9c4;
        border-top: 1px solid #ffd54f;
        border-left: 1px solid #ffd54f;
        transform: rotate(45deg);
        box-shadow: -2px -2px 3px rgba(0, 0, 0, 0.05);
    }
}

/* Dragging state */
.Stashy-highlight-note-display.dragging {
    opacity: 0.95;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25), 0 0 5px rgba(0, 0, 0, 0.3);
    transform: scale(1.05);
    transition: none; /* Disable transitions while dragging */
    pointer-events: none; /* Allow mouse events to pass through while dragging */
    border: 2px solid #ffd54f; /* Thicker border while dragging */
    background-color: #fffde7; /* Slightly lighter background */
    z-index: 10000; /* Ensure it's above other elements */

    /* Removed the rotation animation to prevent blinking during drag */
}

/* Rotation animation for dragging - REMOVED to prevent blinking during drag */

/* Drag handle at the top of the note */
.Stashy-note-drag-handle {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background-color: #ffd54f;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    cursor: grab; /* Change cursor to grab */
    opacity: 0.6;
    /* Removed transition to prevent conflicts during drag */
}

/* Change cursor when actively dragging */
.Stashy-highlight-note-display.dragging,
.Stashy-highlight-note-display.dragging * {
    cursor: grabbing !important;
}

/* DISABLED: Drag handle hover effects to prevent blinking */
.Stashy-highlight-note-display:not(.dragging):hover .Stashy-note-drag-handle {
    opacity: inherit; /* Maintain current opacity */
    height: inherit; /* Maintain current height */
}

/* Add drag indicator dots to the drag handle on hover - DISABLED */
.Stashy-note-drag-handle::before {
    content: '⋮⋮⋮';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    color: rgba(0, 0, 0, 0.3);
    letter-spacing: 3px;
    opacity: 0;
    /* Removed transition to prevent conflicts during drag */
}

/* DISABLED: Drag handle dots hover effect to prevent blinking */
.Stashy-highlight-note-display:not(.dragging):hover .Stashy-note-drag-handle::before {
    opacity: inherit; /* Maintain current opacity */
}

/* Add a subtle hover effect - DISABLED to prevent blinking when hovering over highlights with notes */
mark.Stashy-highlight:hover:not(.has-linked-note) .Stashy-highlight-note-display:not(.dragging) {
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2), 0 0 2px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
    transition: all 0.2s ease; /* Only apply transition on hover, not during drag */
}

/* Completely disable hover effects for note displays on highlights with linked notes */
mark.Stashy-highlight.has-linked-note:hover .Stashy-highlight-note-display {
    box-shadow: inherit; /* Maintain current shadow */
}

/* Add a subtle transition when the note is shown - DISABLED to prevent drag conflicts */
mark.Stashy-highlight .Stashy-highlight-note-display:not(.dragging) {
    transition: none; /* No transition to prevent blinking during drag */
}

/* Drop animation when releasing a dragged note */
.Stashy-highlight-note-display.drop-animation {
    animation: note-drop 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes note-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes note-drop {
    0% {
        transform: scale(1.05);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
    }
    50% {
        transform: scale(1.08);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 2px rgba(0, 0, 0, 0.1);
    }
}

/* Animation for note editor */
@keyframes Stashy-note-editor-fade-in {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}


/* --- Styles for Highlights INSIDE the Note Editor --- */
#Stashy-text mark.Stashy-in-note-highlight {
  padding: 0.1em 0.2em; /* Same padding */
  border-radius: 3px; /* Same radius */
  box-shadow: inset 0 0 0 1px rgba(0,0,0,0.05); /* Subtle inset border */
  color: inherit; /* Inherit text color from note editor */
  /* Use the same background color variables */
  background-color: var(--Stashy-highlight-yellow); /* Default */
}
#Stashy-text mark.Stashy-in-note-highlight.color-yellow { background-color: var(--Stashy-highlight-yellow); }
#Stashy-text mark.Stashy-in-note-highlight.color-pink { background-color: var(--Stashy-highlight-pink); }
#Stashy-text mark.Stashy-in-note-highlight.color-blue { background-color: var(--Stashy-highlight-blue); }
#Stashy-text mark.Stashy-in-note-highlight.color-green { background-color: var(--Stashy-highlight-green); }
#Stashy-text mark.Stashy-in-note-highlight.color-purple { background-color: var(--Stashy-highlight-purple); }

/* --- NEW Highlight Style Classes (In-Note) --- */
#Stashy-text mark.Stashy-in-note-highlight.style-underline {
    text-decoration: underline;
    text-decoration-skip-ink: none;
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important;
    padding: 0;
    box-shadow: none !important;
}
#Stashy-text mark.Stashy-in-note-highlight.style-wavy {
    text-decoration: underline wavy;
    text-decoration-skip-ink: none;
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important;
    padding: 0;
    box-shadow: none !important;
}
#Stashy-text mark.Stashy-in-note-highlight.style-border-thick {
    outline: 2px solid rgba(0, 0, 0, 0.5);
    outline-offset: 1px;
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important;
    padding: 0;
    box-shadow: none !important;
}
/* --- END NEW Highlight Style Classes (In-Note) --- */

/* --- START Add Strikethrough Style (In-Note) --- */
#Stashy-text mark.Stashy-in-note-highlight.style-strikethrough {
    text-decoration: line-through !important; /* Added !important */
    background-color: transparent !important; /* REMOVE background */
    color: inherit !important;
    padding: 0;
    box-shadow: none !important;
}
/* --- END Add Strikethrough Style (In-Note) --- */

/* --- URL and Global Note Button Styles --- */
#Stashy-url-note-btn.active {
    background-color: var(--url-note-active) !important;
    color: white !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
    transform: translateY(1px);
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

#Stashy-global-note-btn.active {
    background-color: var(--global-note-active) !important;
    color: white !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
    transform: translateY(1px);
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* --- Snippet Prefix Styles --- */
.Stashy-snippet-prefix {
    font-weight: bold;
    display: inline-block;
    margin-right: 4px;
    font-size: 0.95em;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    padding-right: 4px;
    font-family: var(--Stashy-font-family);
}

/* --- Snippet Note Styles --- */
.Stashy-snippet-note {
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 6px;
    font-family: var(--Stashy-font-family);
    position: relative;
    border-left: 3px solid #ccc;
}

/* Pro snippet styling */
.Stashy-snippet-note[data-snippet-type="pro"] {
    background-color: rgba(76, 175, 80, 0.1);
    border-left-color: #4CAF50;
}

/* Con snippet styling */
.Stashy-snippet-note[data-snippet-type="con"] {
    background-color: rgba(244, 67, 54, 0.1);
    border-left-color: #F44336;
}

/* Spec snippet styling */
.Stashy-snippet-note[data-snippet-type="spec"] {
    background-color: rgba(33, 150, 243, 0.1);
    border-left-color: #2196F3;
}

/* --- Pro/Con Comparison Styles --- */
.Stashy-comparison-container {
    display: flex;
    width: 100%;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.Stashy-comparison-column {
    flex: 1;
    padding: 0;
    min-width: 0; /* Prevent flex items from overflowing */
}

.Stashy-comparison-column.pro-column {
    border-right: 1px solid #ddd;
}

.Stashy-comparison-header {
    padding: 8px 12px;
    font-weight: bold;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.Stashy-comparison-header.pro-header {
    background-color: rgba(76, 175, 80, 0.1);
}

.Stashy-comparison-header.con-header {
    background-color: rgba(244, 67, 54, 0.1);
}

.Stashy-comparison-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
}

.Stashy-comparison-item:last-child {
    border-bottom: none;
}

.Stashy-comparison-item.pro-item {
    background-color: rgba(76, 175, 80, 0.05);
}

.Stashy-comparison-item.con-item {
    background-color: rgba(244, 67, 54, 0.05);
}

.Stashy-comparison-title {
    text-align: center;
    margin: 10px 0;
    font-size: 16px;
    font-family: var(--Stashy-font-family);
}

.Stashy-comparison-number {
    font-weight: bold;
    margin-right: 5px;
    color: #555;
}

/* --- Privacy Highlight Styles (In-Note) --- */
#Stashy-text mark.Stashy-in-note-highlight.style-blur {
    color: transparent !important;
    text-shadow: 0 0 8px rgba(0,0,0,0.5) !important;
    background-color: rgba(0,0,0,0.05) !important;
    padding: 0.1em 0.2em !important;
    border-radius: 3px !important;
    box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1) !important;
    user-select: none !important;
}

/* Ensure links inside blur highlights are also blurred */
#Stashy-text mark.Stashy-in-note-highlight.style-blur a,
#Stashy-text mark.Stashy-in-note-highlight.style-blur a:link,
#Stashy-text mark.Stashy-in-note-highlight.style-blur a:visited,
#Stashy-text mark.Stashy-in-note-highlight.style-blur a:hover,
#Stashy-text mark.Stashy-in-note-highlight.style-blur a:active {
    color: transparent !important;
    text-shadow: 0 0 8px rgba(0,0,0,0.5) !important;
    text-decoration: none !important;
    pointer-events: none !important; /* Prevent clicking */
}

/* ===================================== */
/*          Element Picker Styles        */
/* ===================================== */
#Stashy-element-picker-overlay {
  pointer-events: none;
  position: fixed;
  border: 2px solid #4285f4;
  background-color: rgba(66, 133, 244, 0.1);
  z-index: 2147483646;
  display: none;
}

#Stashy-element-picker-popup {
  position: absolute;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  padding: 8px;
  z-index: 2147483647;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

#Stashy-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  z-index: 2147483647;
  font-family: Arial, sans-serif;
  font-size: 14px;
  transition: opacity 0.3s ease-in-out;
}

.Stashy-picked-html {
  border-left: 3px solid #4285f4;
  padding-left: 10px;
  margin: 5px 0;
}

/* Apply cursor to all elements when element picker is active */
.Stashy-element-picker-active,
.Stashy-element-picker-active * {
  cursor: crosshair !important;
}

/* Apply grabbing cursor to all elements when dragging a note */
.Stashy-dragging,
.Stashy-dragging * {
  cursor: grabbing !important;
}


/* ===================================== */
/*      Universal Button & Controls      */
/* ===================================== */

#Stashy-container button,
.Stashy-dropdown button,
.Stashy-diagram-editor button,
#Stashy-flashcard-overlay button,
button.Stashy-highlight-palette-swatch { /* Include palette swatches in base styles */
  display: inline-flex; align-items: center; justify-content: center;
  border: none; border-radius: var(--border-radius-md); font-size: 13px; padding: 8px 12px; margin: 2px;
  cursor: pointer;
  transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease, background-color var(--transition-speed) ease, color var(--transition-speed) ease, border-color var(--transition-speed) ease, filter var(--transition-speed) ease;
  font-weight: 500; text-align: center; vertical-align: middle;
  color: var(--Stashy-text-color); font-family: var(--Stashy-font-family);
}
#Stashy-container button:hover:not(:disabled),
.Stashy-dropdown button:hover:not(:disabled),
.Stashy-diagram-editor button:hover:not(:disabled),
#Stashy-flashcard-overlay button:hover:not(:disabled),
button.Stashy-highlight-palette-swatch:hover:not(:disabled) { /* Apply hover to swatches */
  transform: translateY(-1px);
}
#Stashy-container button:active:not(:disabled),
.Stashy-dropdown button:active:not(:disabled),
.Stashy-diagram-editor button:active:not(:disabled),
#Stashy-flashcard-overlay button:active:not(:disabled),
button.Stashy-highlight-palette-swatch:active:not(:disabled) { /* Apply active to swatches */
  transform: translateY(0); filter: brightness(95%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
#Stashy-container button:focus-visible,
.Stashy-dropdown button:focus-visible,
.Stashy-diagram-editor button:focus-visible,
#Stashy-flashcard-overlay button:focus-visible,
button.Stashy-highlight-palette-swatch:focus-visible { /* Apply focus to swatches */
  outline: 2px solid var(--Stashy-accent); outline-offset: 1px;
}
#Stashy-container button:disabled,
.Stashy-dropdown button:disabled,
.Stashy-diagram-editor button:disabled,
#Stashy-flashcard-overlay button:disabled,
button.Stashy-highlight-palette-swatch:disabled { /* Apply disabled to swatches */
  opacity: 0.6; cursor: not-allowed; transform: none; box-shadow: none; filter: none;
}

/* Dropdown Trigger Buttons */
.Stashy-dropdown-button {
  border: 1px solid var(--Stashy-border); padding: 6px 10px; font-size: 12px;
  box-shadow: var(--btn-shadow-light); justify-content: flex-start;
}
#Stashy-format-dropdown-btn { background-color: var(--Stashy-btn-bg-format); }
#Stashy-style-dropdown-btn { background-color: var(--Stashy-btn-bg-style); }
#Stashy-insert-dropdown-btn { background-color: var(--Stashy-btn-bg-insert); }
#Stashy-tools-dropdown-btn { background-color: var(--Stashy-btn-bg-tools); }
#Stashy-view-dropdown-btn { background-color: var(--Stashy-btn-bg-view); }
#Stashy-note-switcher-btn > button { background-color: var(--Stashy-btn-bg-switcher); } /* Target the button inside switcher */
.Stashy-dropdown-button:hover:not(:disabled),
.Stashy-dropdown-button:focus-visible {
  border-color: #adb5bd; box-shadow: var(--btn-shadow-heavy); filter: brightness(98%); /* Subtle hover brightness */
}
.Stashy-dropdown-button:active:not(:disabled) {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15); filter: brightness(95%);
}
.Stashy-dropdown-button .Stashy-icon { margin-right: 6px; font-size: 14px; display: inline-block; line-height: 1; }

/* Tool Buttons (Pin, Global Pin, Minimize, Copy, Close etc.) */
.Stashy-tool-btn {
  padding: 6px 8px; font-size: 16px; line-height: 1;
  background-color: transparent; border: 1px solid transparent; box-shadow: none;
}
.Stashy-tool-btn .Stashy-icon { margin-right: 4px; display: inline-block; }
.Stashy-tool-btn .Stashy-text { font-size: 12px; display: inline-block; vertical-align: middle;} /* Align text vertically */
.Stashy-tool-btn:hover:not(:disabled),
.Stashy-tool-btn:focus-visible {
  background-color: var(--btn-bg-hover); border-color: var(--Stashy-border); box-shadow: none; filter: none;
}
.Stashy-tool-btn:active:not(:disabled) {
  background-color: #e5e7eb; box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1); filter: brightness(97%);
}
/* Active state for Tool Buttons (Pin, Global Pin) */
.Stashy-tool-btn.active {
    background-color: var(--Stashy-accent);
    color: white;
    border-color: var(--Stashy-accent-darker);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
    transform: translateY(0px);
    filter: brightness(100%); /* No extra filter needed */
}
.Stashy-tool-btn.active:hover {
    background-color: var(--Stashy-accent-darker);
    transform: translateY(-1px);
}

/* Recording Button */
.Stashy-tool-btn.recording {
  color: #dc3545; animation: Stashy-pulse 1.5s infinite ease-in-out;
  background-color: #fef2f2; border-color: #fecaca;
}
@keyframes Stashy-pulse {
  0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 0 0 4px rgba(239, 68, 68, 0); }
}

/* Enhanced Voice Typing Styles */
.Stashy-interim-transcript {
  color: #6b7280;
  font-style: italic;
  opacity: 0.8;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  border-radius: 3px;
  padding: 1px 3px;
  margin: 0 1px;
  animation: Stashy-interim-fade-in 0.2s ease-out;
  position: relative;
  transition: all 0.15s ease;
}

.Stashy-interim-transcript::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #3b82f6 50%, transparent 100%);
  animation: Stashy-interim-pulse 1.5s ease-in-out infinite;
}

@keyframes Stashy-interim-fade-in {
  from { opacity: 0; transform: translateY(-2px); }
  to { opacity: 0.8; transform: translateY(0); }
}

@keyframes Stashy-interim-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Voice Status Indicator */
.Stashy-voice-status {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: Stashy-voice-active 2s ease-in-out infinite;
  z-index: 1000;
}

.Stashy-voice-status.listening {
  background: #3b82f6;
  animation: Stashy-voice-listening 1s ease-in-out infinite;
}

.Stashy-voice-status.processing {
  background: #f59e0b;
  animation: Stashy-voice-processing 0.8s linear infinite;
}

.Stashy-voice-status.error {
  background: #ef4444;
  animation: Stashy-voice-error 0.5s ease-in-out 3;
}

@keyframes Stashy-voice-active {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes Stashy-voice-listening {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); }
}

@keyframes Stashy-voice-processing {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

@keyframes Stashy-voice-error {
  0%, 100% { transform: scale(1); }
  25%, 75% { transform: scale(1.2); }
  50% { transform: scale(0.9); }
}

/* Voice notification styles */
.Stashy-voice-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  max-width: 300px;
  animation: Stashy-notification-slide-in 0.3s ease-out;
  font-size: 14px;
  line-height: 1.4;
}

.Stashy-voice-notification.success {
  border-left: 4px solid #10b981;
}

.Stashy-voice-notification.error {
  border-left: 4px solid #ef4444;
}

.Stashy-voice-notification.warning {
  border-left: 4px solid #f59e0b;
}

@keyframes Stashy-notification-slide-in {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes Stashy-notification-slide-out {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(100%); opacity: 0; }
}

/* ARIA live region for screen readers */
.Stashy-voice-aria-live {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Voice command feedback styles */
.Stashy-interim-transcript:has-text([lineBreak]),
.Stashy-interim-transcript:has-text([paragraphBreak]),
.Stashy-interim-transcript:has-text([exclamationMark]),
.Stashy-interim-transcript:has-text([atSymbol]),
.Stashy-interim-transcript:has-text([hashSymbol]),
.Stashy-interim-transcript:has-text([dollarSign]),
.Stashy-interim-transcript:has-text([percentSign]),
.Stashy-interim-transcript:has-text([openBrace]),
.Stashy-interim-transcript:has-text([closeBrace]),
.Stashy-interim-transcript:has-text([colon]),
.Stashy-interim-transcript:has-text([semicolon]),
.Stashy-interim-transcript:has-text([doubleQuote]),
.Stashy-interim-transcript:has-text([openQuote]),
.Stashy-interim-transcript:has-text([closeQuote]),
.Stashy-interim-transcript:has-text([apostrophe]),
.Stashy-interim-transcript:has-text([period]),
.Stashy-interim-transcript:has-text([comma]),
.Stashy-interim-transcript:has-text([questionMark]),
.Stashy-interim-transcript:has-text([plusSign]),
.Stashy-interim-transcript:has-text([minusSign]),
.Stashy-interim-transcript:has-text([equalsSign]),
.Stashy-interim-transcript:has-text([asterisk]),
.Stashy-interim-transcript:has-text([forwardSlash]),
.Stashy-interim-transcript:has-text([backslash]),
.Stashy-interim-transcript:has-text([openParenthesis]),
.Stashy-interim-transcript:has-text([closeParenthesis]),
.Stashy-interim-transcript:has-text([openBracket]),
.Stashy-interim-transcript:has-text([closeBracket]),
.Stashy-interim-transcript:has-text([underscore]),
.Stashy-interim-transcript:has-text([pipe]),
.Stashy-interim-transcript:has-text([ampersand]) {
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.05) 100%);
  border-left: 2px solid #10b981;
  padding-left: 6px;
}

/* Command indicator styling within interim text */
.Stashy-interim-transcript {
  position: relative;
}

.Stashy-interim-transcript:contains('[lineBreak]')::after {
  content: '↵';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[paragraphBreak]')::after {
  content: '¶';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

/* Symbol command indicators */
.Stashy-interim-transcript:contains('[exclamationMark]')::after {
  content: '!';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

.Stashy-interim-transcript:contains('[atSymbol]')::after {
  content: '@';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[hashSymbol]')::after {
  content: '#';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

.Stashy-interim-transcript:contains('[dollarSign]')::after {
  content: '$';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[percentSign]')::after {
  content: '%';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[openBrace]')::after {
  content: '{';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[closeBrace]')::after {
  content: '}';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[colon]')::after {
  content: ':';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

.Stashy-interim-transcript:contains('[semicolon]')::after {
  content: ';';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

.Stashy-interim-transcript:contains('[doubleQuote]')::after,
.Stashy-interim-transcript:contains('[openQuote]')::after,
.Stashy-interim-transcript:contains('[closeQuote]')::after {
  content: '"';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[apostrophe]')::after {
  content: "'";
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

/* Basic punctuation indicators */
.Stashy-interim-transcript:contains('[period]')::after {
  content: '.';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

.Stashy-interim-transcript:contains('[comma]')::after {
  content: ',';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

.Stashy-interim-transcript:contains('[questionMark]')::after {
  content: '?';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 14px;
}

/* Mathematical/Technical symbol indicators */
.Stashy-interim-transcript:contains('[plusSign]')::after {
  content: '+';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[minusSign]')::after {
  content: '-';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[equalsSign]')::after {
  content: '=';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[asterisk]')::after {
  content: '*';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[forwardSlash]')::after {
  content: '/';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[backslash]')::after {
  content: '\\';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

/* Bracket indicators */
.Stashy-interim-transcript:contains('[openParenthesis]')::after {
  content: '(';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[closeParenthesis]')::after {
  content: ')';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[openBracket]')::after {
  content: '[';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[closeBracket]')::after {
  content: ']';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

/* Additional symbol indicators */
.Stashy-interim-transcript:contains('[underscore]')::after {
  content: '_';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[pipe]')::after {
  content: '|';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

.Stashy-interim-transcript:contains('[ampersand]')::after {
  content: '&';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  font-size: 12px;
}

/* Enhanced voice status for command processing */
.Stashy-voice-status.command {
  background: #10b981;
  animation: Stashy-voice-command 0.6s ease-in-out;
}

@keyframes Stashy-voice-command {
  0%, 100% { transform: scale(1); background: #10b981; }
  50% { transform: scale(1.4); background: #059669; }
}

/* Voice modifier key active state */
.Stashy-voice-modifier-active {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  border-color: #f59e0b !important;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3) !important;
  animation: Stashy-modifier-pulse 1.5s ease-in-out infinite !important;
}

.Stashy-voice-modifier-active .Stashy-voice-status {
  background: #f59e0b !important;
  animation: Stashy-modifier-status-pulse 1s ease-in-out infinite !important;
}

/* Modifier key pulse animation */
@keyframes Stashy-modifier-pulse {
  0%, 100% {
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.5);
  }
}

@keyframes Stashy-modifier-status-pulse {
  0%, 100% {
    background: #f59e0b;
    transform: scale(1);
  }
  50% {
    background: #fbbf24;
    transform: scale(1.2);
  }
}

/* Formatting Buttons (inside dropdown) */
.Stashy-format-btn {
  background-color: var(--btn-bg-light); border: 1px solid var(--Stashy-border);
  font-weight: 600; padding: 6px 10px; box-shadow: var(--btn-shadow-light); min-width: 32px;
}

.Stashy-format-btn:hover:not(:disabled):not(.active),
.Stashy-format-btn:focus-visible:not(.active) {
  background-color: var(--btn-bg-hover); border-color: #d1d5db; box-shadow: var(--btn-shadow-light); filter: none;
}
/* Active state for formatting buttons */
.Stashy-format-btn.active {
  background-color: var(--light-grey-active); /* Light grey */
  color: var(--darker-text-on-light); /* Darker text for contrast */
  border-color: #adb5bd; /* Slightly darker border */
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
  transform: translateY(0px);
  filter: none;
}
.Stashy-format-btn.active:hover {
  background-color: #d1d5db; /* Slightly darker grey on hover */
  filter: none;
  transform: translateY(-1px);
}
.Stashy-format-btn del { text-decoration: line-through; }

/* Text Size Buttons */
.Stashy-text-size-btn {
  background-color: var(--btn-bg-light); border: 1px solid var(--Stashy-border);
  padding: 6px 10px; box-shadow: var(--btn-shadow-light);
}

/* Global Font Size Control */
.Stashy-global-font-size-control {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--Stashy-border);
}

.Stashy-font-size-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  background: linear-gradient(to right, #ddd 0%, var(--Stashy-accent) 100%);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.Stashy-font-size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--Stashy-accent);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.Stashy-font-size-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.Stashy-font-size-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--Stashy-accent);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}



/* Block format styling for better visual feedback */
#Stashy-text blockquote {
  border-left: 4px solid var(--Stashy-accent);
  margin: 8px 0;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0 4px 4px 0;
  font-style: italic;
  position: relative;
}

#Stashy-text blockquote::before {
  content: "💬 Quote (Press Esc to exit)";
  position: absolute;
  top: -18px;
  left: 0;
  font-size: 10px;
  color: var(--Stashy-accent);
  font-style: normal;
  font-weight: 500;
  opacity: 0.7;
}

#Stashy-text pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin: 8px 0;
  font-family: 'Courier New', Consolas, monospace;
  font-size: 13px;
  line-height: 1.4;
  overflow-x: auto;
  position: relative;
}

#Stashy-text pre::before {
  content: "💻 Code (Press Esc to exit)";
  position: absolute;
  top: -18px;
  left: 0;
  font-size: 10px;
  color: var(--Stashy-accent);
  font-family: system-ui, -apple-system, sans-serif;
  font-weight: 500;
  opacity: 0.7;
}


.Stashy-text-size-btn:hover:not(:disabled),
.Stashy-text-size-btn:focus-visible {
  background-color: var(--btn-bg-hover); border-color: #d1d5db; box-shadow: var(--btn-shadow-light); filter: none;
}

/* ===================================== */
/*             Dropdown Menu             */
/* ===================================== */
.Stashy-dropdown { position: relative; display: inline-block; }
.Stashy-dropdown-content {
  display: none; position: absolute; background-color: #ffffff; min-width: 160px; /* Slightly wider */
  max-width: 220px; /* Prevent excessive width */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); z-index: 1001; border-radius: var(--border-radius-md);
  padding: 8px; margin-top: 4px; right: 0; /* Increased padding */
  max-height: 300px; /* Limit height */
  overflow-y: auto; /* Add scroll if needed */
  /* Hidden scrollbar by default */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent transparent; /* Firefox - invisible by default */
  -ms-overflow-style: -ms-autohiding-scrollbar; /* IE and Edge - auto-hiding */
}
/* Adjust dropdown position for specific buttons */
#Stashy-format-dropdown-btn + .Stashy-dropdown-content { left: 0; right: auto; }
#Stashy-note-switcher-btn + .Stashy-dropdown-content { left: 0; right: auto; min-width: 120px; }

/* Base style for buttons inside dropdowns */
.Stashy-dropdown-content button {
  display: block; width: 100%; text-align: left; margin: 2px 0; font-size: 13px;
  padding: 8px 10px; background-color: transparent; border: none;
  border-radius: var(--border-radius-sm); /* Slightly smaller radius inside */
  justify-content: flex-start; /* Align text/icon left */
}
/* Hover for non-active items inside dropdown */
.Stashy-dropdown-content button:hover:not(:disabled):not(.active) {
  background-color: var(--btn-bg-hover); filter: none; transform: none;
}
/* Ensure icons align properly in dropdown buttons */
.Stashy-dropdown-content button .Stashy-icon {
    margin-right: 8px; /* Consistent icon spacing */
    display: inline-block;
    width: 1.2em; /* Give icon fixed width */
    text-align: center;
}

/* WebKit scrollbar styles for dropdown menus */
.Stashy-dropdown-content::-webkit-scrollbar {
  width: 6px; /* Slightly narrower than text area scrollbar */
  background-color: transparent; /* Transparent background */
}

.Stashy-dropdown-content::-webkit-scrollbar-thumb {
  background-color: transparent; /* Invisible by default */
  border-radius: 3px; /* Rounded corners */
  transition: background-color 0.3s ease; /* Smooth transition */
}

/* Show scrollbar only on hover */
.Stashy-dropdown-content:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3); /* Semi-transparent thumb when hovering */
}

.Stashy-dropdown-content:hover::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5); /* Darker on hover */
}

/* Firefox hover styles */
.Stashy-dropdown-content:hover {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox - visible on hover */
}



/* Add note button */
.Stashy-note-switch-btn.add-note { font-style: italic; color: var(--Stashy-accent); background-color: transparent; }
.Stashy-note-switch-btn.add-note:hover { background-color: #dcfce7; } /* Lighter green hover */
/* Active note button */
.Stashy-note-switch-btn.active {
  background-color: var(--light-green-active); /* Use light green */
  color: var(--Stashy-accent-darker); /* Darker green text */
  font-weight: 600; cursor: default;
  filter: none; box-shadow: inset 0 1px 1px rgba(0,0,0,0.05);
}
.Stashy-note-switch-btn.active:hover {
  background-color: #bbf7d0; /* Slightly darker green on hover */
  filter: none;
}

/* Inline elements */
.Stashy-dropdown-group .Stashy-color-swatch,
.Stashy-dropdown-group .Stashy-opacity-btn,
.Stashy-dropdown-group .Stashy-text-shadow-btn,
.Stashy-dropdown-group .Stashy-format-btn,
.Stashy-dropdown-group .Stashy-text-size-btn,
.Stashy-dropdown-group .Stashy-tool-btn,
.Stashy-dropdown-group input[type="color"] /* Target color input */
{
  display: inline-block;
  width: auto;
  margin: 4px;
  vertical-align: middle;
}
.Stashy-dropdown-group .Stashy-color-swatch { width: 24px; height: 24px; padding: 0; }

.Stashy-dropdown-content.show { display: block; }
.Stashy-dropdown-group { border-top: 1px solid var(--Stashy-border); margin-top: 8px; padding-top: 8px; } /* More spacing */
.Stashy-dropdown-group:first-child { border-top: none; margin-top: 0; padding-top: 0; }
.Stashy-dropdown-group-title {
  display: block; font-size: 11px; font-weight: 600; color: var(--Stashy-text-light);
  padding: 4px 6px; margin-bottom: 4px; text-transform: uppercase; letter-spacing: 0.5px;
}

/* Color Swatches (for Note Theme) */
.Stashy-color-swatch {
  width: 28px; height: 28px; padding: 0; border-radius: 50%;
  border: 2px solid var(--Stashy-border);
  transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease, border-color var(--transition-speed) ease, background-color var(--transition-speed) ease;
  box-shadow: var(--btn-shadow-light); vertical-align: middle; margin: 3px; display: inline-block;
  background-color: white; /* Default to white if no color class */
}
.Stashy-color-swatch:hover:not(.active),
.Stashy-color-swatch:focus-visible:not(.active) {
  transform: scale(1.15); border-color: #9ca3af; box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  outline: none; filter: none;
}
/* Active state for color swatches */
.Stashy-color-swatch.active {
  border: 2px solid var(--Stashy-accent-darker);
  transform: scale(1.1);
  box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.7),
              inset 0 0 0 4px var(--Stashy-accent-darker);
}
/* Specific background colors */
.Stashy-color-swatch.color-yellow { background-color: var(--Stashy-bg-yellow); }
.Stashy-color-swatch.color-blue { background-color: var(--Stashy-bg-blue); }
.Stashy-color-swatch.color-green { background-color: var(--Stashy-bg-green); }
.Stashy-color-swatch.color-pink { background-color: var(--Stashy-bg-pink); }
.Stashy-color-swatch.color-purple { background-color: var(--Stashy-bg-purple); }
.Stashy-color-swatch.color-orange { background-color: var(--Stashy-bg-orange); }
.Stashy-color-swatch.color-teal { background-color: var(--Stashy-bg-teal); }
.Stashy-color-swatch.color-red { background-color: var(--Stashy-bg-red); }
.Stashy-color-swatch.color-brown { background-color: var(--Stashy-bg-brown); }
.Stashy-color-swatch.color-gray { background-color: var(--Stashy-bg-gray); }


/* --- Opacity Buttons --- */
.Stashy-opacity-btn {
  font-size: 12px; padding: 6px 10px; background-color: var(--btn-bg-hover); /* Default light grey */
  border: 1px solid #d1d5db; box-shadow: var(--btn-shadow-light);
}
.Stashy-opacity-btn:hover:not(:disabled):not(.active),
.Stashy-opacity-btn:focus-visible:not(.active) {
  background-color: #e5e7eb; border-color: #adb5bd; box-shadow: var(--btn-shadow-light); filter: none;
}
/* Active state for opacity buttons */
.Stashy-opacity-btn.active {
  background-color: var(--light-grey-active); /* Light grey active */
  color: var(--darker-text-on-light); /* Dark text */
  font-weight: 600; transform: scale(1.05) translateY(0);
  border-color: #9ca3af; /* Darker border */
  filter: none;
  box-shadow: inset 0 1px 1px rgba(0,0,0,0.1);
}
.Stashy-opacity-btn.active:hover {
  background-color: #d1d5db; /* Slightly darker grey */
  filter: none;
}

/* --- Text Shadow Button --- */
.Stashy-text-shadow-btn {
  font-size: 16px; padding: 6px 10px; background-color: var(--btn-bg-light);
  border: 1px solid var(--Stashy-border); box-shadow: var(--btn-shadow-light);
  min-width: 32px;
}
.Stashy-text-shadow-btn:hover:not(:disabled):not(.active),
.Stashy-text-shadow-btn:focus-visible:not(.active) {
  background-color: var(--btn-bg-hover); border-color: #d1d5db; box-shadow: var(--btn-shadow-light); filter: none;
}
/* Active state for text shadow button */
.Stashy-text-shadow-btn.active {
  background-color: var(--light-grey-active); /* Light grey active */
  color: var(--darker-text-on-light); /* Dark text */
  font-weight: 600; transform: scale(1.05) translateY(0);
  border-color: #9ca3af; /* Darker border */
  filter: none;
  box-shadow: inset 0 1px 1px rgba(0,0,0,0.1);
}
.Stashy-text-shadow-btn.active:hover {
  background-color: #d1d5db; /* Slightly darker grey */
  filter: none;
  transform: translateY(-1px);
}

/* Template Select Styling */
.Stashy-template-select,
.Stashy-font-family-select {
    display: block; width: calc(100% - 8px); margin: 4px; padding: 8px 10px;
    border: 1px solid var(--Stashy-border); border-radius: var(--border-radius-md); background-color: #fff;
    font-family: var(--Stashy-font-family); font-size: 13px; cursor: pointer;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="5" viewBox="0 0 10 5"><path fill="%236b7280" d="M0 0l5 5 5-5z"/></svg>'); /* Updated color */
    background-repeat: no-repeat; background-position: right 10px center; background-size: 10px 5px;
    padding-right: 30px; /* Space for arrow */
}

/* Font family selector specific styles */
.Stashy-font-family-select {
    max-width: 200px;
    transition: all 0.2s ease;
}

.Stashy-font-family-select option {
    padding: 8px;
    font-size: 13px;
}

/* Group font options with optgroup */
.Stashy-font-family-select optgroup {
    font-weight: bold;
    color: #000000; /* Changed from accent color to black */
    background-color: #f9f9f9;
}


.Stashy-template-select:hover,
.Stashy-font-family-select:hover { border-color: #adb5bd; }

.Stashy-template-select:focus,
.Stashy-font-family-select:focus {
    outline: none;
    border-color: var(--Stashy-accent);
    box-shadow: 0 0 0 2px rgba(22, 163, 74, 0.2);
} /* Adjusted focus shadow color */

/* ===================================== */
/*        Note Structure & Layout        */
/* ===================================== */
#Stashy-toggle {
  position: fixed; bottom: 24px; right: 24px; z-index: 9998;
  width: 48px; height: 48px; /* Size for icon image */
  padding: 0; border: none; background: transparent; /* Remove all container styling */
  cursor: pointer; border-radius: 8px; /* Subtle rounded corners */
  transition: all var(--transition-speed) ease, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* Subtle shadow for the icon */
}
#Stashy-toggle:hover,
#Stashy-toggle:focus-visible {
  transform: translateY(-2px) scale(1.1); /* Lift and scale the icon */
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25); /* Enhanced shadow */
  filter: brightness(1.1) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)); /* Brighten and add glow */
}
#Stashy-toggle:active {
  transform: translateY(0px) scale(0.95); /* Pressed state */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  filter: brightness(0.9); /* Slightly darken when pressed */
}

#Stashy-container {
  position: absolute; top: 100px; right: 24px; width: 340px; height: 380px; /* Increased height */
  min-width: 240px; min-height: 150px; /* Adjusted min-height for new toolbar */
  border: 1px solid var(--Stashy-border);
  border-radius: 10px; /* Softer radius */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); /* Slightly larger shadow */
  z-index: 9999; display: none; flex-direction: column; overflow: hidden; resize: none; /* CHANGED resize: both to resize: none */
  background-color: var(--Stashy-bg-yellow);
  transition: opacity 0.3s ease, transform 0.2s ease, height 0.2s ease, min-height 0.2s ease, width 0.2s ease; /* Added width transition */
  opacity: 0; transform: scale(0.98); cursor: auto;

  /* CSS Containment for performance optimization */
  contain: content; /* Isolate this subtree from the rest of the page for layout/paint optimization */
  will-change: transform, opacity; /* Hint to browser to optimize these properties */
}

/* Iframe container styles for YouTube and other sites with keyboard conflicts */
#Stashy-container.Stashy-iframe-container {
  /* Inherit all the base container styles but ensure iframe compatibility */
  background-color: transparent; /* Let iframe handle background */
}

#Stashy-container.Stashy-iframe-container #Stashy-note-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 10px; /* Match container radius */
  background: transparent;
  /* Ensure iframe is properly contained */
  contain: content;
}


#Stashy-container::-webkit-scrollbar { display: none; }
#Stashy-container { scrollbar-width: none; -ms-overflow-style: none; }
#Stashy-container.visible { display: flex; opacity: 1; transform: scale(1); }
#Stashy-container.pinned { position: fixed; }
/* Minimize functionality removed */
#Stashy-container::-webkit-resizer { display: none; /* Hide default resizer */ }

/* Global note badge */
#Stashy-container.global-note {
  position: relative; /* For badge positioning */
  border: 2px solid #3498db; /* Blue border for global notes */
}

#Stashy-container.global-note::before {
  content: "🌐";
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #3498db;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 10001; /* Above other note elements */
}
#Stashy-container.theme-yellow { background-color: var(--Stashy-bg-yellow); }
#Stashy-container.theme-blue { background-color: var(--Stashy-bg-blue); }
#Stashy-container.theme-green { background-color: var(--Stashy-bg-green); }
#Stashy-container.theme-pink { background-color: var(--Stashy-bg-pink); }
#Stashy-container.theme-purple { background-color: var(--Stashy-bg-purple); }
#Stashy-container.theme-orange { background-color: var(--Stashy-bg-orange); }
#Stashy-container.theme-teal { background-color: var(--Stashy-bg-teal); }
#Stashy-container.theme-red { background-color: var(--Stashy-bg-red); }
#Stashy-container.theme-brown { background-color: var(--Stashy-bg-brown); }
#Stashy-container.theme-gray { background-color: var(--Stashy-bg-gray); }



#Stashy-header {
  padding: 10px 14px; /* Slightly more padding */
  cursor: grab; /* Changed from 'move' to 'grab' for better visual feedback */
  border-top-left-radius: 9px; border-top-right-radius: 9px; /* Match container */
  border-bottom: 1px solid rgba(0, 0, 0, 0.08); /* Softer border */
  font-weight: 600; display: flex; align-items: center; flex-wrap: wrap; gap: 8px; /* More gap */
  font-family: var(--Stashy-font-family); font-size: 14px; color: var(--Stashy-text-color);
  user-select: none; background-color: var(--Stashy-header-yellow);
  transition: background-color 0.3s ease, cursor 0.2s ease; /* Added cursor to transition */
  position: relative; z-index: 10; min-height: 48px; flex-shrink: 0;
}
#Stashy-container.theme-yellow #Stashy-header { background-color: var(--Stashy-header-yellow); }
#Stashy-container.theme-blue #Stashy-header { background-color: var(--Stashy-header-blue); }
#Stashy-container.theme-green #Stashy-header { background-color: var(--Stashy-header-green); }
#Stashy-container.theme-pink #Stashy-header { background-color: var(--Stashy-header-pink); }
#Stashy-container.theme-purple #Stashy-header { background-color: var(--Stashy-header-purple); }
#Stashy-container.theme-orange #Stashy-header { background-color: var(--Stashy-header-orange); }
#Stashy-container.theme-teal #Stashy-header { background-color: var(--Stashy-header-teal); }
#Stashy-container.theme-red #Stashy-header { background-color: var(--Stashy-header-red); }
#Stashy-container.theme-brown #Stashy-header { background-color: var(--Stashy-header-brown); }
#Stashy-container.theme-gray #Stashy-header { background-color: var(--Stashy-header-gray); }



/* --- NEW: Title Input Styling --- */
#Stashy-note-title-input {
  flex-grow: 1; /* Allow input to take available space */
  font-weight: 600;
  font-size: 15px; /* Match previous title span */
  color: inherit; /* Inherit color from header */
  background-color: var(--header-input-bg);
  border: none;
  outline: none;
  padding: 4px 6px; /* Add some padding */
  margin: -4px -6px; /* Counteract padding to keep alignment */
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  min-width: 100px; /* Prevent it from becoming too small */
  margin-right: auto; /* Push other controls to the right */
  cursor: text; /* Indicate editable */
  line-height: 1.4; /* Ensure text isn't cut off */
  vertical-align: middle; /* Align with other header items */
}
#Stashy-note-title-input::placeholder {
  color: inherit; /* Use header text color */
  opacity: 0.7; /* Make placeholder slightly faded */
  font-style: italic;
}
#Stashy-note-title-input:focus {
  background-color: var(--header-input-bg-focus);
  box-shadow: 0 0 0 2px var(--header-input-border-focus); /* Focus ring */
  cursor: text;
}
/* --- End Title Input Styling --- */

#Stashy-timestamp {
  font-size: 10px;
  color: var(--Stashy-text-light);
  margin-left: 6px;
  white-space: nowrap;
  flex-shrink: 0;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}
@keyframes reminderFlash {
  0%, 100% { background-color: inherit; }
  50% { background-color: var(--Stashy-reminder-flash); box-shadow: inset 0 0 10px rgba(253, 224, 71, 0.6); }
}
#Stashy-header.Stashy-reminder-flash { animation: reminderFlash 2.5s ease-in-out; }
#Stashy-header-controls { display: flex; align-items: center; gap: 4px; margin-left: auto; position: relative; z-index: 10; flex-shrink: 0; }
#Stashy-note-switcher-btn { order: -1; margin-right: 8px; }

#Stashy-text {
  flex-grow: 1; padding: 12px; /* Slightly reduced padding to give more space */
  margin: 8px 8px 4px 8px; /* Reduced bottom margin */
  font-size: var(--Stashy-default-font-size, 14px);
  line-height: 1.6; /* Better line height */
  outline: none; box-sizing: border-box; overflow-y: auto;
  font-family: var(--Stashy-font-family); color: var(--Stashy-text-color);
  background-color: rgba(255, 255, 255, 0.3); /* Slightly more opaque */
  border: none; border-radius: var(--border-radius-md);
  position: relative; z-index: 1; cursor: text;
  /* Text shadow removed as requested */
  /* Hidden scrollbar by default */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent transparent; /* Firefox - invisible by default */
  -ms-overflow-style: -ms-autohiding-scrollbar; /* IE and Edge - auto-hiding */
}

/* Text shadow effect when enabled - Enhanced 3D appearance with directional offset */
#Stashy-text.Stashy-text-shadow-enabled,
#Stashy-text.Stashy-text-shadow-enabled * {
  text-shadow:
    1px 2px 3px rgba(0, 0, 0, 0.4),
    2px 3px 6px rgba(0, 0, 0, 0.2) !important;
}

/* Enhanced 3D shadow for images when text shadow is enabled */
#Stashy-text.Stashy-text-shadow-enabled img {
  filter:
    drop-shadow(1px 2px 3px rgba(0, 0, 0, 0.4))
    drop-shadow(2px 3px 6px rgba(0, 0, 0, 0.2)) !important;
}
#Stashy-text:focus { background-color: rgba(255, 255, 255, 0.5); } /* More opaque focus */



/* WebKit scrollbar styles for #Stashy-text */
#Stashy-text::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
  background-color: transparent; /* Transparent background */
}

#Stashy-text::-webkit-scrollbar-thumb {
  background-color: transparent; /* Invisible by default */
  border-radius: 4px; /* Rounded corners */
  transition: background-color 0.3s ease; /* Smooth transition */
}

/* Show scrollbar only on hover */
#Stashy-text:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3); /* Semi-transparent thumb when hovering */
}

#Stashy-text:hover::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5); /* Darker on hover */
}

/* Firefox hover styles */
#Stashy-text:hover {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox - visible on hover */
}


.Stashy-equation { background-color: rgba(0,0,0,0.05); padding: 1px 4px; border-radius: 3px; cursor: pointer; display: inline-block; margin: 0 1px; vertical-align: baseline; } /* Adjust vertical alignment */
.Stashy-equation:hover { background-color: rgba(0,0,0,0.1); }

/* ===== ENHANCED EQUATION EDITOR STYLES ===== */

/* Modal overlay with enhanced backdrop */
#Stashy-equation-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 10002;
    display: none;
    justify-content: center;
    align-items: center;
    font-family: var(--Stashy-font-family);
    animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
}

/* Enhanced modal content container - centered and properly sized */
.Stashy-equation-editor-content {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 16px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    width: 85%;
    max-width: 1000px;
    height: 75%;
    max-height: 700px;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: modalSlideIn 0.3s ease-out;
    z-index: 10001;
}

.Stashy-equation-editor-content.enhanced-modal {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.Stashy-equation-editor-content.enhanced-modal:hover {
    box-shadow:
        0 25px 70px rgba(0, 0, 0, 0.35),
        0 10px 40px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Responsive design */
@media (max-width: 1200px) {
    .Stashy-equation-editor-content {
        width: 95%;
        height: 90%;
        max-width: none;
    }
}

@media (max-width: 768px) {
    .Stashy-equation-editor-content {
        width: 98%;
        height: 95%;
        border-radius: 12px;
        flex-direction: column;
    }

    .Stashy-equation-editor-main {
        flex-direction: column !important;
    }

    .Stashy-equation-editor-right {
        width: 100% !important;
        max-height: 300px;
    }
}

/* Simplified Header - no drag functionality */
.Stashy-equation-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(229, 231, 235, 0.6);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px 16px 0 0;
    position: relative;
    overflow: hidden;
}

.Stashy-equation-editor-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

/* Title container */
.Stashy-title-container {
    flex: 1;
    margin-left: 8px;
}

.Stashy-equation-editor-header h3 {
    margin: 0;
    font-size: 22px;
    font-weight: 700;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.Stashy-equation-editor-header h3 .title-text {
    background: linear-gradient(135deg, #1f2937, #4f46e5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.Stashy-editor-subtitle {
    font-size: 13px;
    color: #6b7280;
    margin-top: 2px;
    font-weight: 400;
}

/* Header controls */
.Stashy-header-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.Stashy-header-btn {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(209, 213, 219, 0.6);
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
}

.Stashy-header-btn:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(156, 163, 175, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.Stashy-header-btn.minimize-btn {
    color: #059669;
}

.Stashy-header-btn.minimize-btn:hover {
    background: rgba(16, 185, 129, 0.1);
    border-color: #10b981;
    color: #047857;
}

.Stashy-header-btn.close-btn {
    color: #dc2626;
}

.Stashy-header-btn.close-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
    color: #b91c1c;
}

/* Main content area - ensure proper layout */
.Stashy-equation-editor-main {
    display: flex;
    flex: 1;
    overflow: hidden;
    min-height: 0; /* Allow flex shrinking */
}

/* Left panel - Input and preview */
.Stashy-equation-editor-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 24px;
    border-right: 1px solid #e5e7eb;
}

.Stashy-equation-input-section {
    margin-bottom: 24px;
}

.Stashy-equation-input-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.Stashy-equation-latex-input {
    width: 100%;
    height: 120px;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.Stashy-equation-latex-input:focus {
    outline: none;
    border-color: var(--Stashy-accent);
    box-shadow: 0 0 0 3px rgba(127, 243, 98, 0.1);
}

.Stashy-equation-preview-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.Stashy-equation-preview-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.Stashy-equation-preview {
    flex: 1;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    background: #f9fafb;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 150px;
    font-size: 18px;
    overflow: auto;
}

/* Right panel - Library and symbols */
.Stashy-equation-editor-right {
    width: 400px;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

/* Enhanced Tabs with modern design */
.Stashy-equation-tabs {
    display: flex;
    border-bottom: 1px solid rgba(229, 231, 235, 0.6);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 8px 8px 0 8px;
    gap: 4px;
}

.Stashy-equation-tabs.enhanced-tabs {
    border-radius: 0;
    background: transparent;
    padding: 12px 16px 0 16px;
}

.Stashy-equation-tab {
    position: relative;
    padding: 8px 12px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    color: #6b7280;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 6px 6px 0 0;
    min-width: 65px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    overflow: hidden;
}

.Stashy-equation-tab.enhanced-tab {
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(229, 231, 235, 0.6);
    border-bottom: none;
    backdrop-filter: blur(8px);
}

.Stashy-equation-tab:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #374151;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.Stashy-equation-tab.active {
    color: var(--Stashy-accent-darker);
    background: white;
    border-color: rgba(229, 231, 235, 0.8);
    transform: translateY(0);
    box-shadow:
        0 -4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.Stashy-equation-tab.enhanced-tab.active::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--Stashy-accent), var(--Stashy-accent-darker));
    border-radius: 2px 2px 0 0;
}

/* Tab content elements - smaller sizes */
.tab-icon {
    font-size: 14px;
    transition: transform 0.2s ease;
}

.tab-text {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--Stashy-accent);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.Stashy-equation-tab:hover .tab-icon {
    transform: scale(1.1);
}

.Stashy-equation-tab.active .tab-indicator {
    width: 80%;
}

/* Tab animations */
@keyframes tabSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.Stashy-equation-tab {
    animation: tabSlideIn 0.3s ease-out;
}

.Stashy-equation-tab:nth-child(1) { animation-delay: 0.1s; }
.Stashy-equation-tab:nth-child(2) { animation-delay: 0.15s; }
.Stashy-equation-tab:nth-child(3) { animation-delay: 0.2s; }
.Stashy-equation-tab:nth-child(4) { animation-delay: 0.25s; }
.Stashy-equation-tab:nth-child(5) { animation-delay: 0.3s; }

/* Tab content */
.Stashy-equation-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.Stashy-equation-tab-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 16px;
    overflow-y: auto;
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.Stashy-equation-tab-content.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Library styles */
.Stashy-equation-search {
    margin-bottom: 16px;
}

.Stashy-equation-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.Stashy-equation-search-input:focus {
    outline: none;
    border-color: var(--Stashy-accent);
    box-shadow: 0 0 0 2px rgba(127, 243, 98, 0.1);
}

.Stashy-equation-category {
    margin-bottom: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
}

.Stashy-equation-category-header {
    padding: 12px 16px;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
    position: relative;
}

.Stashy-equation-category-header:hover {
    background-color: #f9fafb;
}

.Stashy-equation-category-header::after {
    content: '▼';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    transition: transform 0.2s ease;
}

.Stashy-equation-category-header.expanded::after {
    transform: translateY(-50%) rotate(180deg);
}

.Stashy-equation-category-content {
    padding: 8px;
}

.Stashy-equation-item {
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: 8px;
    border: 1px solid transparent;
}

.Stashy-equation-item:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

.Stashy-equation-name {
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
    font-size: 14px;
}

.Stashy-equation-item-preview {
    margin: 8px 0;
    padding: 8px;
    background: #f9fafb;
    border-radius: 4px;
    text-align: center;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.Stashy-equation-description {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

/* Symbols styles */
.Stashy-symbol-category {
    margin-bottom: 20px;
}

.Stashy-symbol-category-header {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.Stashy-symbols-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 8px;
}

.Stashy-symbol-button {
    width: 40px;
    height: 40px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
}

.Stashy-symbol-button:hover {
    background-color: #f3f4f6;
    border-color: var(--Stashy-accent);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.Stashy-symbol-button:active {
    transform: translateY(0);
    background-color: #e5e7eb;
}

/* User equations styles */
.Stashy-user-equations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
}

.Stashy-user-equations-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
}

.Stashy-add-equation-btn {
    padding: 8px 12px;
    background: var(--Stashy-accent);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.Stashy-add-equation-btn:hover {
    background: var(--Stashy-accent-darker);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.Stashy-user-equation-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.Stashy-user-equation-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.Stashy-user-equation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.Stashy-user-equation-name {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.Stashy-user-equation-actions {
    display: flex;
    gap: 4px;
}

.Stashy-user-equation-btn {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.Stashy-user-equation-btn:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.Stashy-user-equation-delete {
    color: #dc2626;
    border-color: #fca5a5;
}

.Stashy-user-equation-delete:hover {
    background-color: #fef2f2;
    border-color: #dc2626;
}

.Stashy-user-equation-preview {
    margin: 8px 0;
    padding: 8px;
    background: #f9fafb;
    border-radius: 4px;
    text-align: center;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.Stashy-user-equation-description {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
    margin-top: 4px;
}

.Stashy-empty-message {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 20px;
    line-height: 1.6;
}

/* Enhanced list items for recent, favorites, and user equations */
.Stashy-equation-list-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.Stashy-equation-list-item:hover {
    border-color: var(--Stashy-accent);
    box-shadow: 0 4px 12px rgba(127, 243, 98, 0.15);
    transform: translateY(-1px);
}

.Stashy-equation-list-item.recent-item {
    border-left: 3px solid #3b82f6;
}

.Stashy-equation-list-item.favorite-item {
    border-left: 3px solid #f59e0b;
}

.Stashy-equation-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.Stashy-equation-item-name {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.Stashy-equation-item-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.Stashy-equation-list-item:hover .Stashy-equation-item-actions {
    opacity: 1;
}

.Stashy-equation-action-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.Stashy-equation-action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.Stashy-equation-action-btn.use-btn:hover {
    background: rgba(16, 185, 129, 0.1);
    border-color: #10b981;
}

.Stashy-equation-action-btn.favorite-btn:hover {
    background: rgba(245, 158, 11, 0.1);
    border-color: #f59e0b;
}

.Stashy-equation-action-btn.delete-btn:hover,
.Stashy-equation-action-btn.unfavorite-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
}

.Stashy-equation-timestamp {
    font-size: 11px;
    color: #9ca3af;
    text-align: right;
    margin-top: 4px;
    font-style: italic;
}

/* Recent and favorites headers */
.Stashy-recent-header,
.Stashy-favorites-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.Stashy-recent-header h4,
.Stashy-favorites-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
}

.Stashy-clear-recent-btn {
    padding: 4px 8px;
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.Stashy-clear-recent-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
}

/* Footer styles - ensure full visibility */
.Stashy-equation-editor-footer {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;
    flex-shrink: 0; /* Prevent footer from shrinking */
    min-height: 70px; /* Ensure minimum height for buttons */
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.Stashy-equation-editor-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.Stashy-equation-btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.Stashy-equation-btn-primary {
    background: var(--Stashy-accent);
    color: white;
    border-color: var(--Stashy-accent);
}

.Stashy-equation-btn-primary:hover {
    background: var(--Stashy-accent-darker);
    border-color: var(--Stashy-accent-darker);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.Stashy-equation-btn-secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;
}

.Stashy-equation-btn-secondary:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}





/* Equation styling in note text area - clean and natural */
#Stashy-text .Stashy-equation-span,
#Stashy-text .Stashy-equation {
    display: inline !important;
    font-size: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    font-family: inherit !important;
    vertical-align: baseline !important;
    margin: 0 2px !important;
    padding: 1px 3px !important;
    background: rgba(0, 0, 0, 0.02) !important;
    border: 1px solid transparent !important;
    border-radius: 2px !important;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    outline: none;
}

/* Hover effect for editing */
#Stashy-text .Stashy-equation-span:hover,
#Stashy-text .Stashy-equation:hover {
    background: rgba(127, 243, 98, 0.1) !important;
    border-color: rgba(127, 243, 98, 0.3) !important;
    cursor: pointer;
}

/* Ensure KaTeX elements inherit text styling */
#Stashy-text .Stashy-equation-span .katex,
#Stashy-text .Stashy-equation .katex {
    font-size: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
}

#Stashy-text .Stashy-equation-span .katex-html,
#Stashy-text .Stashy-equation .katex-html {
    font-size: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
}

/* Ensure equations don't interfere with text flow */
#Stashy-text .Stashy-equation-span .katex .base,
#Stashy-text .Stashy-equation .katex .base {
    display: inline !important;
    vertical-align: baseline !important;
}

/* Make sure equations can be selected and cursor can be positioned around them */
#Stashy-text .Stashy-equation-span,
#Stashy-text .Stashy-equation {
    user-select: none; /* Prevent text selection inside equation */
}

/* Allow cursor positioning before and after equations */
#Stashy-text .Stashy-equation-span::before,
#Stashy-text .Stashy-equation::before,
#Stashy-text .Stashy-equation-span::after,
#Stashy-text .Stashy-equation::after {
    content: '';
    display: inline;
    width: 0;
    height: 0;
}

/* ===== END ADVANCED EQUATION EDITOR STYLES =====
#Stashy-text blockquote { margin: 12px 0 12px 20px; padding: 10px 15px; border-left: 4px solid var(--Stashy-border); background-color: rgba(0, 0, 0, 0.02); color: var(--Stashy-text-light); font-style: italic; border-radius: 0 4px 4px 0; }

/* --- Linked Note Style --- */
#Stashy-text blockquote.Stashy-linked-note {
  border-left: 4px solid #ffcc00;
  background-color: rgba(255, 204, 0, 0.05);
  position: relative;
  padding-top: 30px;
  margin-top: 16px;
}

#Stashy-text .Stashy-linked-note-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 4px 15px;
  background-color: rgba(255, 204, 0, 0.1);
  font-size: 12px;
  font-weight: bold;
  color: #996600;
  border-bottom: 1px solid rgba(255, 204, 0, 0.2);
  font-style: normal;
}
#Stashy-text pre { background-color: rgba(0, 0, 0, 0.05); border: 1px solid var(--Stashy-border); border-radius: var(--border-radius-md); padding: 12px; margin: 12px 0; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; color: var(--Stashy-text-color); overflow-x: auto; white-space: pre-wrap; word-wrap: break-word; }
#Stashy-text pre code { background-color: transparent; padding: 0; border: none; font-family: inherit; font-size: inherit; white-space: inherit; }
#Stashy-text ul, #Stashy-text ol { margin: 12px 0 12px 30px; padding-left: 0; }
#Stashy-text li { margin-bottom: 5px; }
#Stashy-text img { max-width: 100%; height: auto; margin: 6px 0; cursor: default; display: block; border-radius: 4px; }
#Stashy-text .Stashy-inserted-image { border: 1px solid var(--Stashy-border); display: block; margin: 10px auto; max-width: 95%; border-radius: var(--border-radius-sm); }
/* Highlight for in-note search */
mark.Stashy-in-note-highlight { background-color: #ffd54f; color: #111; padding: 0.1em; border-radius: 2px; box-shadow: 0 0 0 1px #ffb74d; }
mark.Stashy-in-note-highlight.active { background-color: #ff8a65; color: #fff; box-shadow: 0 0 0 2px #ff7043;}

/* Text alignment classes */
#Stashy-text .text-align-left { text-align: left; }
#Stashy-text .text-align-center { text-align: center; }
#Stashy-text .text-align-right { text-align: right; }
#Stashy-text .text-align-justify { text-align: justify; }

/* Links in notes */
#Stashy-text a {
  color: #1a73e8; /* Google blue */
  text-decoration: none;
  border-bottom: 1px solid rgba(26, 115, 232, 0.3); /* Subtle underline */
  padding: 0 2px; /* Small padding for better clickability */
  margin: 0 1px; /* Small margin for better spacing */
  border-radius: 2px; /* Slight rounding */
  transition: background-color 0.2s ease, border-bottom-color 0.2s ease, color 0.2s ease;
  cursor: pointer !important; /* Force pointer cursor */
  position: relative; /* For hover effect */
}

#Stashy-text a:hover {
  background-color: rgba(26, 115, 232, 0.1); /* Light blue background on hover */
  border-bottom-color: #1a73e8; /* Darker underline on hover */
  color: #0d47a1; /* Darker blue text on hover */
}

/* Special styling for links inserted by Element Picker */
#Stashy-text a[contenteditable="false"] {
  display: inline-block; /* Make it a block to improve clickability */
  padding: 2px 6px; /* More padding for better clickability */
  margin: 2px 0; /* Add some vertical margin */
  background-color: rgba(26, 115, 232, 0.05); /* Very light blue background */
  border: 1px solid rgba(26, 115, 232, 0.2); /* Light blue border */
  border-radius: 4px; /* Rounded corners */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* Subtle shadow */
  max-width: 100%; /* Prevent overflow */
  overflow-wrap: break-word; /* Allow breaking long URLs */
  word-break: break-all; /* Break long URLs */
}

#Stashy-text a[contenteditable="false"]:hover {
  background-color: rgba(26, 115, 232, 0.1); /* Slightly darker blue on hover */
  border-color: rgba(26, 115, 232, 0.4); /* Darker border on hover */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Enhanced shadow on hover */
}

/* Add a small icon to indicate external link */
#Stashy-text a[contenteditable="false"]::after {
  content: "↗"; /* External link arrow */
  display: inline-block;
  margin-left: 4px;
  font-size: 0.8em;
  opacity: 0.7;
}


/* ===================================== */
/*      Quick Snippets Toolbar           */
/* ===================================== */

.Stashy-quick-snippets-toolbar {
  display: flex;
  justify-content: flex-start; /* Align buttons to the start */
  align-items: center;
  gap: 3px; /* Reduced space between buttons */
  padding: 1px 6px 0px 6px; /* Further reduced padding */
  margin: 0 6px 2px 6px; /* Reduced margins */
  /* Optional: Add a subtle background or border */
  /* background-color: rgba(0, 0, 0, 0.02); */
  /* border-top: 1px solid var(--Stashy-border); */
  flex-shrink: 0; /* Prevent toolbar from shrinking */
  position: relative; /* Changed to relative for palette positioning */
  z-index: 2;
}

/* Target all tool buttons within snippets bar */
.Stashy-quick-snippets-toolbar button.Stashy-tool-btn {
  padding: 3px 4px; /* Further reduced padding */
  font-size: 16px; /* Smaller font size */
  line-height: 1;
  background-color: var(--btn-bg-light);
  border: 1px solid var(--Stashy-border);
  box-shadow: var(--btn-shadow-light);
  border-radius: var(--border-radius-sm);
  min-width: 26px; /* Reduced minimum width */
  position: relative;
}

.Stashy-quick-snippets-toolbar button.Stashy-tool-btn:hover:not(:disabled) {
  transform: translateY(-1px); background-color: var(--btn-bg-hover); border-color: #d1d5db;
  box-shadow: 0 1px 2px rgba(0,0,0,0.08); filter: none;
}
.Stashy-quick-snippets-toolbar button.Stashy-tool-btn:active:not(:disabled) {
  transform: translateY(0); background-color: #e5e7eb;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1); filter: brightness(97%);
}


/* Hide text in ALL snippet buttons */
.Stashy-quick-snippets-toolbar button.Stashy-tool-btn .Stashy-text {
   display: none;
}
/* Ensure icon centered */
.Stashy-quick-snippets-toolbar button.Stashy-tool-btn .Stashy-icon {
  margin-right: 0; display: inline-block;
}

/* Highlight with Note button removed as requested */

/* --- Highlight Color Palette Styles --- */
.Stashy-highlight-palette {
    display: none; /* Hidden by default */
    position: absolute; /* Positioned relative to toolbar */
    background-color: #ffffff;
    border: 1px solid var(--Stashy-border);
    border-radius: var(--border-radius-md);
    padding: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1002; /* Above other toolbars/note elements */
    /* flex-wrap: wrap; */ /* Remove wrapping if using column layout */
    flex-direction: column; /* Stack style and color rows */
    gap: 6px; /* Space between rows and items */
    width: auto; /* Allow width to adjust */
    min-width: 200px; /* Ensure enough width for snippet buttons */
}



/* NEW: Style selector row */
.Stashy-highlight-style-selector {
    display: flex;
    gap: 5px;
    justify-content: center;
    padding-bottom: 4px; /* Space below styles */
    border-bottom: 1px solid var(--Stashy-border-light, #eee); /* Separator */
    margin-bottom: 4px; /* Space above colors */
}

/* Snippet selector styles */
.Stashy-highlight-snippet-selector {
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-top: 4px;
    justify-content: center;
}

.Stashy-highlight-separator {
    margin-top: 4px;
    padding-top: 4px;
    border-top: 1px solid #eee;
    height: 1px;
}

.Stashy-highlight-snippet-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
    border: 1px solid #eee;
    background-color: #fff;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
}

.Stashy-highlight-snippet-btn:hover {
    background-color: #f5f5f5;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.Stashy-highlight-snippet-btn:active {
    background-color: #e0e0e0;
    transform: translateY(0);
    box-shadow: none;
}

.Stashy-highlight-snippet-btn .Stashy-icon {
    font-size: 16px;
}

/* NEW: Style buttons */
button.Stashy-highlight-style-btn {
    /* Inherit some base button styles maybe? Or define own */
    display: inline-flex; align-items: center; justify-content: center;
    border: 1px solid var(--Stashy-border);
    border-radius: var(--border-radius-sm);
    padding: 4px 6px;
    margin: 0;
    background-color: #f8f9fa;
    cursor: pointer;
    font-size: 13px;
    line-height: 1;
    box-shadow: var(--btn-shadow-light);
    transition: background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
}
button.Stashy-highlight-style-btn .Stashy-icon {
    font-size: 14px; /* Adjust icon size if needed */
    display: inline-block;
    margin: 0; /* Remove margin if icon-only */
}

button.Stashy-highlight-style-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    box-shadow: var(--btn-shadow-medium);
}





/* NEW: Color selector row */
.Stashy-highlight-color-selector {
    display: flex;
    flex-wrap: wrap; /* Allow colors to wrap */
    gap: 6px;
    justify-content: center;
}

/* RESTORED: Swatch style within palette */
button.Stashy-highlight-palette-swatch {
    /* Inherits base button style from Universal section */
    display: block;
    width: 24px; /* Fixed size */
    height: 24px;
    padding: 0; /* Remove padding */
    margin: 0; /* Reset margin */
    border-radius: 50%; /* Make them circles */
    border: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
    box-shadow: var(--btn-shadow-light);
    transition: transform 0.15s ease, box-shadow 0.15s ease, border-color 0.15s ease; /* Added */
}

button.Stashy-highlight-palette-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--btn-shadow-heavy);
  border-color: rgba(0,0,0,0.2); /* Added hover border */
 }
/* --- END RESTORED --- */


/* --- End Quick Snippets Toolbar --- */

/* Container for tags and reminder inputs */
#Stashy-metadata-container {
  max-height: 36px; /* Further reduced height */
  overflow-y: auto; /* Enable vertical scrolling */
  margin: 0px 6px 6px 6px; /* Reduced margins */
  border-radius: var(--border-radius-md);
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0; /* Prevent container from shrinking */
  position: relative;
  z-index: 2;
  padding: 2px; /* Reduced padding */
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  /* Hide scrollbar for Firefox */
  scrollbar-width: none;
  /* Hide scrollbar for IE and Edge */
  -ms-overflow-style: none;
}

/* Add hover effect to indicate interactivity */
#Stashy-metadata-container:hover {
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}



/* Input wrapper styling */
.Stashy-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  margin: 1px 0; /* Reduced margin */
}



#Stashy-copy-code-btn:hover {
  background-color: var(--btn-bg-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#Stashy-copy-code-btn:active {
  transform: translateY(0);
  background-color: var(--light-grey-active);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}



/* Input label styling */
.Stashy-input-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* Reduced width */
  height: 28px; /* Reduced height */
  font-size: 11px; /* Smaller font */
  color: var(--Stashy-text-light);
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid var(--Stashy-border);
  border-right: none;
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
  flex-shrink: 0;
}

#Stashy-tags, #Stashy-reminder {
  width: calc(100% - 20px); /* Adjust for reduced label width */
  padding: 4px 8px; /* Further reduced padding */
  margin: 0; /* No margin since wrapper has margin */
  border: 1px solid var(--Stashy-border);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
  font-size: 11px; /* Smaller font size */
  font-family: var(--Stashy-font-family);
  background-color: rgba(255, 255, 255, 0.4);
  color: var(--Stashy-text-color);
  transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
  min-height: 28px; /* Further reduced height */
  position: relative;
  cursor: text;
}

/* Special styling for tags input in tags-notebook wrapper */
.Stashy-tags-notebook-wrapper #Stashy-tags {
  border-right: none; /* Remove right border to connect with notebook selector */
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  flex-grow: 1;
}



/* Add a more noticeable indicator at the bottom of the container to show scrollable content */
/* Hide scrollbar for Webkit browsers */
#Stashy-metadata-container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

/* No animations needed anymore */
#Stashy-tags:focus, #Stashy-reminder:focus {
  border-color: var(--Stashy-accent);
  background-color: rgba(255, 255, 255, 0.6);
  outline: none;
  box-shadow: 0 0 0 2px rgba(22, 163, 74, 0.2); /* Focus shadow */
  z-index: 1; /* Ensure focus ring is visible */
}



/* When input is focused, also highlight its label */
.Stashy-input-wrapper:focus-within .Stashy-input-label {
  border-color: var(--Stashy-accent);
  background-color: rgba(255, 255, 255, 0.3);
  color: var(--Stashy-accent);
}
#Stashy-tags::placeholder, #Stashy-reminder::placeholder { color: var(--Stashy-text-light); }

/* ===================================== */
/*         Compact Notebook Selector     */
/* ===================================== */

/* Special styling for tags wrapper with notebook selector */
.Stashy-tags-notebook-wrapper {
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid var(--Stashy-border);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: all 0.2s ease;
}

.Stashy-tags-notebook-wrapper:hover {
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Notebook selector container */
.Stashy-notebook-selector {
  position: relative;
  display: flex;
  align-items: center;
  border-left: 1px solid var(--Stashy-border);
  background-color: rgba(255, 255, 255, 0.1);
  /* Ensure dropdown has proper stacking context */
  z-index: 10001; /* Higher than note container but lower than dropdown */
}

/* Notebook toggle button */
.Stashy-notebook-toggle {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 3px 5px; /* Reduced padding */
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 9px; /* Smaller font */
  color: var(--Stashy-text-color);
  transition: all 0.2s ease;
  min-width: 60px; /* Reduced width */
  height: 28px; /* Reduced height to match input fields */
  box-sizing: border-box;
}

.Stashy-notebook-toggle:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.Stashy-notebook-toggle.active {
  background-color: rgba(255, 255, 255, 0.4);
}

.Stashy-notebook-icon {
  font-size: 12px; /* Increased icon size for better visibility */
  flex-shrink: 0;
}

.Stashy-notebook-current {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600; /* Increased font weight for better readability */
  text-align: left;
  font-size: 11px; /* Increased font size for better readability */
}

.Stashy-notebook-arrow {
  font-size: 9px; /* Increased arrow size for better visibility */
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.Stashy-notebook-toggle.active .Stashy-notebook-arrow {
  transform: rotate(180deg);
}

/* Notebook dropdown */
.Stashy-notebook-dropdown {
  position: fixed; /* Changed from absolute to fixed to escape parent container constraints */
  min-width: 150px;
  max-width: 200px;
  max-height: 200px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--Stashy-border);
  border-radius: var(--border-radius-md);
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
  z-index: 2147483647; /* Highest possible z-index - above snippet buttons (2147483646) and all other elements */
  overflow-y: auto;
  backdrop-filter: blur(10px);
  /* CRITICAL: Break out of parent stacking context */
  isolation: isolate; /* Create new stacking context */
  contain: none; /* Prevent containment from interfering */
  /* Animation for smooth appearance */
  opacity: 0;
  transform: translateY(5px) scale(0.95);
  transition: opacity 0.2s ease, transform 0.2s ease;
  /* Hidden scrollbar by default */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

/* Dropdown visible state */
.Stashy-notebook-dropdown[style*="visibility: visible"] {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Dropdown positioned below (when not enough space above) */
.Stashy-notebook-dropdown[style*="top: 100%"] {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Downward shadow when below */
}

/* Ensure dropdown is always above other elements when visible */
.Stashy-notebook-dropdown[style*="display: block"] {
  z-index: 2147483647 !important; /* Force highest z-index when visible */
}

/* Force top z-index class for problematic cases */
.Stashy-notebook-dropdown-force-top {
  z-index: 2147483647 !important; /* Force highest z-index with !important */
  position: fixed !important; /* Ensure fixed positioning context */
  isolation: isolate !important; /* Force new stacking context */
  contain: none !important; /* Prevent containment interference */
}

/* Ensure dropdown is always on document body with proper stacking */
body > .Stashy-notebook-dropdown {
  z-index: 2147483647 !important;
  isolation: isolate !important;
  contain: none !important;
  position: fixed !important;
}

/* WebKit scrollbar styles for dropdown */
.Stashy-notebook-dropdown::-webkit-scrollbar {
  width: 4px;
  background-color: transparent;
}

.Stashy-notebook-dropdown::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 2px;
  transition: background-color 0.3s ease;
}

/* Show scrollbar only on hover */
.Stashy-notebook-dropdown:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

.Stashy-notebook-dropdown:hover::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

/* Notebook dropdown items */
.Stashy-notebook-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: var(--Stashy-text-color);
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease;
  text-align: left;
  width: 100%;
  box-sizing: border-box;
  min-height: 32px;
}

.Stashy-notebook-item:hover {
  background-color: rgba(255, 255, 255, 0.7);
  transform: translateX(2px);
}

.Stashy-notebook-item.selected {
  background-color: var(--Stashy-accent);
  color: white;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.Stashy-notebook-item.selected:hover {
  background-color: var(--Stashy-accent-darker);
  transform: translateX(0);
}

.Stashy-notebook-item-icon {
  font-size: 12px;
  flex-shrink: 0;
  width: 16px;
  text-align: center;
}

.Stashy-notebook-item-name {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

/* ===================================== */
/*    Hierarchical Notebook Dropdown     */
/* ===================================== */

/* Indentation for hierarchical display */
.Stashy-notebook-item-indent {
  color: var(--Stashy-accent);
  font-weight: 500;
  font-family: monospace;
  opacity: 0.7;
  flex-shrink: 0;
  font-size: 10px;
  line-height: 1;
}

/* Level-specific indent styling */
.Stashy-notebook-item-indent[data-level="1"] {
  color: rgba(76, 175, 80, 0.8);
}

.Stashy-notebook-item-indent[data-level="2"] {
  color: rgba(255, 152, 0, 0.8);
  font-size: 9px;
}

.Stashy-notebook-item-indent[data-level="3"] {
  color: rgba(156, 39, 176, 0.8);
  font-size: 8px;
}

/* Sub-notebook item styling */
.Stashy-notebook-item-sub {
  background-color: rgba(76, 175, 80, 0.05);
  border-left: 3px solid transparent;
}

.Stashy-notebook-item-sub:hover {
  background-color: rgba(76, 175, 80, 0.1);
  border-left-color: rgba(76, 175, 80, 0.3);
}

/* Level-specific styling */
.Stashy-notebook-item-level-1 {
  background-color: rgba(76, 175, 80, 0.08);
  border-left-color: rgba(76, 175, 80, 0.6);
  font-size: 11px;
}

.Stashy-notebook-item-level-1:hover {
  background-color: rgba(76, 175, 80, 0.15);
  border-left-color: rgba(76, 175, 80, 0.8);
}

.Stashy-notebook-item-level-2 {
  background-color: rgba(255, 152, 0, 0.08);
  border-left-color: rgba(255, 152, 0, 0.6);
  font-size: 10px;
}

.Stashy-notebook-item-level-2:hover {
  background-color: rgba(255, 152, 0, 0.15);
  border-left-color: rgba(255, 152, 0, 0.8);
}

.Stashy-notebook-item-level-3 {
  background-color: rgba(156, 39, 176, 0.08);
  border-left-color: rgba(156, 39, 176, 0.6);
  font-size: 9px;
}

.Stashy-notebook-item-level-3:hover {
  background-color: rgba(156, 39, 176, 0.15);
  border-left-color: rgba(156, 39, 176, 0.8);
}

/* Enhanced main notebook styling */
.Stashy-notebook-item[data-level="0"] {
  font-weight: 600;
  background-color: rgba(33, 150, 243, 0.05);
  border-left: 3px solid rgba(33, 150, 243, 0.3);
}

.Stashy-notebook-item[data-level="0"]:hover {
  background-color: rgba(33, 150, 243, 0.1);
  border-left-color: rgba(33, 150, 243, 0.6);
}

/* Ungrouped option styling */
.Stashy-notebook-item[data-notebook-id="UNGROUPED"] {
  background-color: rgba(158, 158, 158, 0.05);
  border-left: 3px solid rgba(158, 158, 158, 0.3);
  font-style: italic;
}

.Stashy-notebook-item[data-notebook-id="UNGROUPED"]:hover {
  background-color: rgba(158, 158, 158, 0.1);
  border-left-color: rgba(158, 158, 158, 0.6);
}

/* Ensure proper spacing and alignment */
.Stashy-notebook-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  min-height: 28px;
  transition: all 0.15s ease;
}

/* Icon adjustments for hierarchy */
.Stashy-notebook-item-level-1 .Stashy-notebook-item-icon {
  font-size: 11px;
}

.Stashy-notebook-item-level-2 .Stashy-notebook-item-icon {
  font-size: 10px;
}

.Stashy-notebook-item-level-3 .Stashy-notebook-item-icon {
  font-size: 9px;
}

/* Name text adjustments */
.Stashy-notebook-item-level-1 .Stashy-notebook-item-name {
  font-size: 11px;
}

.Stashy-notebook-item-level-2 .Stashy-notebook-item-name {
  font-size: 10px;
}

.Stashy-notebook-item-level-3 .Stashy-notebook-item-name {
  font-size: 9px;
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
  .Stashy-notebook-item {
    padding: 4px 6px;
    min-height: 24px;
    gap: 2px;
  }

  .Stashy-notebook-item-indent {
    font-size: 8px;
  }

  .Stashy-notebook-item-indent[data-level="2"],
  .Stashy-notebook-item-indent[data-level="3"] {
    font-size: 7px;
  }

  .Stashy-notebook-item-level-1 {
    font-size: 10px;
  }

  .Stashy-notebook-item-level-2 {
    font-size: 9px;
  }

  .Stashy-notebook-item-level-3 {
    font-size: 8px;
  }

  .Stashy-notebook-item-level-1 .Stashy-notebook-item-name {
    font-size: 10px;
  }

  .Stashy-notebook-item-level-2 .Stashy-notebook-item-name {
    font-size: 9px;
  }

  .Stashy-notebook-item-level-3 .Stashy-notebook-item-name {
    font-size: 8px;
  }

  .Stashy-notebook-item-level-1 .Stashy-notebook-item-icon,
  .Stashy-notebook-item-level-2 .Stashy-notebook-item-icon,
  .Stashy-notebook-item-level-3 .Stashy-notebook-item-icon {
    font-size: 8px;
  }
}



/* Minimize functionality completely removed */


/* Diagram Editor Styles */
/* ... (Keep existing styles) ... */
.Stashy-diagram-editor {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #ffffff;
    padding: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    z-index: 10000;
    border-radius: 12px;
    width: 1000px; /* Increased from 750px */
    height: 700px; /* Increased from 500px */
    max-width: 95vw; /* Responsive to screen size */
    max-height: 95vh; /* Responsive to screen size */
    display: flex;
    flex-direction: column;
    gap: 15px;
    font-family: Arial, sans-serif;
    border: 1px solid #ccc;
    box-sizing: border-box;
    --bg-color: #ffffff;
    --text-color: #333333;
    --border-color: #cccccc;
    --toolbar-bg: #e8ecef;
    --canvas-bg: #f8f8f8;
    --shape-bg: #ffffff;
    --shape-hover-bg: #f5f5f5;
    --button-hover-bg: #e9ecef;
    --button-active-bg: #e0e0e0;
}



.Stashy-diagram-editor h4 {
    color: var(--text-color);
    margin: 0;
    padding: 0;
    font-size: 16px;
    user-select: none;
}

.Stashy-diagram-editor h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
    text-align: center;
    color: var(--text-color);
}

.Stashy-diagram-editor div[style*="flex-grow: 1"] {
    position: relative;
    background-color: var(--canvas-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: auto; /* Changed from hidden to auto for scrolling when zoomed */
    flex-grow: 1;
    /* Smooth scrolling for better user experience */
    scroll-behavior: smooth;
}

.Stashy-diagram-editor canvas {
    cursor: crosshair;
    display: block;
}

.Stashy-diagram-toolbar {
    display: flex;
    gap: 5px;
    padding: 8px;
    background: var(--toolbar-bg);
    border-radius: 8px;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--border-color);
    flex-shrink: 0;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

/* Zoom control styling */
.Stashy-diagram-toolbar span {
    font-size: 12px;
    color: var(--text-color);
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid var(--border-color);
}

/* Responsive design for smaller screens */
@media (max-width: 1200px) {
    .Stashy-diagram-editor {
        width: 90vw;
        height: 80vh;
    }
}

@media (max-width: 768px) {
    .Stashy-diagram-editor {
        width: 95vw;
        height: 85vh;
        padding: 10px;
    }

    .Stashy-diagram-toolbar {
        padding: 6px;
        gap: 3px;
    }

    .Stashy-diagram-toolbar button {
        padding: 6px 8px;
        font-size: 12px;
    }

    .Stashy-diagram-toolbar span {
        font-size: 11px;
        padding: 1px 4px;
    }
}

/* Title bar controls */
.Stashy-diagram-control-btn {
    background: transparent;
    border: none;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-color);
    font-size: 16px;
    padding: 0;
    transition: background-color 0.2s;
}

.Stashy-diagram-control-btn:hover {
    background-color: var(--button-hover-bg);
}

/* Tool buttons */
.Stashy-diagram-toolbar button.Stashy-tool-btn {
    background-color: var(--shape-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 4px;
    padding: 5px 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.Stashy-diagram-toolbar button.Stashy-tool-btn:hover {
    background-color: var(--button-hover-bg);
    transform: translateY(-1px);
    box-shadow: 0 2px 3px rgba(0,0,0,0.1);
}

.Stashy-diagram-toolbar button.Stashy-tool-btn.active {
    background-color: var(--button-active-bg);
    border-color: var(--text-color);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
    transform: none;
}

/* Shapes Library Styles */
.Stashy-shapes-library {
    width: 120px;
    background-color: var(--canvas-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.Stashy-shape-item {
    background-color: var(--shape-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 5px;
    cursor: grab;
    text-align: center;
    transition: all 0.2s ease;
    color: var(--text-color);
}

.Stashy-shape-item:hover {
    background-color: var(--shape-hover-bg);
    border-color: var(--text-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.Stashy-shape-item:active {
    cursor: grabbing;
}

/* Help dialog */
.Stashy-diagram-help {
    color: var(--text-color);
    background-color: var(--bg-color);
}

.Stashy-diagram-help h3,
.Stashy-diagram-help h4 {
    color: var(--text-color);
}

.Stashy-diagram-help ul {
    padding-left: 20px;
}

/* Selected shape */
.Stashy-shape-selected {
    outline: 2px dashed #4285f4 !important;
    outline-offset: 2px;
}
.Stashy-diagram-toolbar > div { display: flex; align-items: center; gap: 5px; }
.Stashy-diagram-toolbar button.Stashy-tool-btn { font-size: 14px; padding: 5px 4px; background-color: #fff; border: 1px solid #ccc; }
.Stashy-diagram-toolbar button.Stashy-tool-btn:hover:not(:disabled), .Stashy-diagram-toolbar button.Stashy-tool-btn:focus-visible { background-color: #e9ecef; border-color: #bbb; }
.Stashy-diagram-toolbar button.Stashy-tool-btn:disabled { opacity: 0.5; cursor: not-allowed; background-color: #eee; transform: none; box-shadow: none; }
.Stashy-diagram-toolbar button.Stashy-tool-btn.active { background: var(--Stashy-accent); color: #ffffff; border-color: var(--Stashy-accent-darker); box-shadow: inset 0 1px 2px rgba(0,0,0,0.2); }
.Stashy-diagram-toolbar button.Stashy-tool-btn .Stashy-text { display: none; }
.Stashy-color-picker { width: 30px; height: 30px; border: 1px solid #ced4da; border-radius: 4px; cursor: pointer; padding: 0; vertical-align: middle; }
.Stashy-color-picker::-webkit-color-swatch-wrapper { padding: 2px; }
.Stashy-color-picker::-webkit-color-swatch { border: none; border-radius: 3px; }
.Stashy-color-picker::-moz-color-swatch { border: none; border-radius: 3px; }
.Stashy-brush-slider { width: 80px; height: 15px; accent-color: var(--Stashy-accent); vertical-align: middle; }


/* Flashcard Modal Styles */
/* ... (Keep existing styles) ... */
#Stashy-flashcard-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); z-index: 10001; display: none; justify-content: center; align-items: center; }
#Stashy-flashcard-modal {
    background-color: #ffffff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    min-height: 250px;
    display: flex;
    flex-direction: column;
    position: relative;
    font-family: var(--Stashy-font-family);

    /* Performance optimizations */
    contain: content; /* Isolate this subtree for layout/paint optimization */
    will-change: transform, opacity; /* Hint to browser to optimize these properties */
    backface-visibility: hidden; /* Prevent flickering in some browsers */
}
#Stashy-flashcard-modal h4 { text-align: center; margin: 0 0 15px 0; font-size: 18px; color: var(--Stashy-accent); font-weight: 600; }
#Stashy-flashcard-content { flex-grow: 1; border: 1px solid #eee; border-radius: 8px; padding: 20px; margin-bottom: 20px; font-size: 18px; line-height: 1.6; overflow-y: auto; text-align: center; display: flex; flex-direction: column; justify-content: center; align-items: center; cursor: pointer; min-height: 150px; color: var(--Stashy-text-color); }
#Stashy-flashcard-content .flashcard-reveal-hint { font-size: 13px; color: var(--Stashy-text-light); margin-top: 10px; font-style: italic; text-align: center; }
#Stashy-flashcard-content .flashcard-front { font-weight: normal; margin-bottom: 10px; }
#Stashy-flashcard-content .flashcard-back { color: var(--Stashy-accent); font-weight: 500; margin-top: 10px; }
#Stashy-flashcard-content hr { border: none; border-top: 1px solid #eee; width: 80%; margin: 10px 0; }
#Stashy-flashcard-controls { display: flex; justify-content: space-between; align-items: center; gap: 10px; flex-shrink: 0; }
#Stashy-flashcard-nav { display: flex; gap: 8px; }
#Stashy-flashcard-nav button { padding: 8px 16px; font-size: 14px; background-color: var(--btn-bg-light); border: 1px solid #ced4da; border-radius: 6px; color: var(--Stashy-text-color); cursor: pointer; box-shadow: var(--btn-shadow-light); transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease, background-color var(--transition-speed) ease; }
#Stashy-flashcard-nav button:hover:not(:disabled), #Stashy-flashcard-nav button:focus-visible { background-color: #e9ecef; transform: translateY(-1px); box-shadow: var(--btn-shadow-heavy); outline: none; }
#Stashy-flashcard-nav button:disabled { background-color: #f8f9fa; color: #adb5bd; cursor: not-allowed; transform: none; box-shadow: none; border-color: #e9ecef; }
#Stashy-flashcard-counter { font-size: 13px; color: #6c757d; }
#Stashy-flashcard-close-btn { position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 24px; color: #aaa; cursor: pointer; padding: 5px; line-height: 1; }
#Stashy-flashcard-close-btn:hover { color: #333; }


/* Interactive Checklist Styles */
/* ... (Keep existing styles) ... */
#Stashy-text ul.Stashy-checklist { list-style: none; padding-left: 0.5em; margin-top: 0.5em; margin-bottom: 0.5em; }
#Stashy-text li.Stashy-checklist-item { list-style: none; margin-bottom: 0.3em; padding-left: 1.8em; position: relative; cursor: pointer; transition: color 0.2s ease, text-decoration 0.2s ease; }
#Stashy-text li.Stashy-checklist-item::before { content: '☐'; position: absolute; left: 0; top: 0.05em; font-size: 1.2em; line-height: 1; color: var(--Stashy-text-light); cursor: pointer; width: 1.5em; text-align: center; }
#Stashy-text li.Stashy-checklist-item.checked::before { content: '☑'; color: var(--Stashy-accent); font-weight: bold; }
#Stashy-text li.Stashy-checklist-item.checked { color: var(--Stashy-text-light); text-decoration: line-through; }


/* Timestamp Link Styling */
/* ... (Keep existing styles) ... */
#Stashy-text .Stashy-timestamp-link { color: var(--Stashy-timestamp-color); cursor: pointer; text-decoration: none; border-bottom: 1px dotted var(--Stashy-timestamp-color); padding: 0 2px; border-radius: 3px; transition: background-color 0.2s ease, color 0.2s ease; white-space: nowrap; }
#Stashy-text .Stashy-timestamp-link:hover { background-color: var(--Stashy-timestamp-hover-bg); color: #074197; text-decoration: none; border-bottom-style: solid; }


/* Timestamp Video Title/URL Styling */
/* ... (Keep existing styles) ... */
#Stashy-text h3 { margin-top: 1em; margin-bottom: 0.2em; font-size: 1.1em; font-weight: 600; color: var(--Stashy-text-color); padding-bottom: 0.1em; border: none; }
#Stashy-text p.Stashy-video-associated-url { font-size: 0.85em; color: var(--Stashy-text-light); margin: 0 0 0.5em 0; padding: 0; line-height: 1.3; word-break: break-all; }
#Stashy-text p.Stashy-video-associated-url a { color: var(--Stashy-timestamp-color); text-decoration: none; border-bottom: 1px dotted var(--Stashy-timestamp-color); transition: color 0.2s ease, border-bottom-style 0.2s ease; }
#Stashy-text p.Stashy-video-associated-url a:hover { color: #074197; border-bottom-style: solid; }
#Stashy-text p.Stashy-video-associated-url + ul { margin-top: 0.5em; }
#Stashy-text h3 + ul { margin-top: 0.5em; }


/* In-Note Search Bar Styles */
/* ... (Keep existing styles) ... */
#Stashy-in-note-search-bar { display: none; align-items: center; gap: 5px; padding: 5px 8px; margin: 0 8px 4px 8px; background-color: rgba(200, 200, 200, 0.15); border-top: 1px solid var(--Stashy-border); border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm); flex-shrink: 0; position: relative; z-index: 3; }
#Stashy-in-note-search-input { flex-grow: 1; padding: 5px 8px; font-size: 12px; border: 1px solid var(--Stashy-border); border-radius: var(--border-radius-sm); outline: none; }
#Stashy-in-note-search-input:focus { border-color: var(--Stashy-accent); box-shadow: 0 0 0 1px var(--Stashy-accent); }
#Stashy-in-note-search-bar button.Stashy-tool-btn { padding: 3px 5px; font-size: 14px; background-color: #fff; border: 1px solid #ccc; min-width: 26px; box-shadow: var(--shadow-sm); }
#Stashy-in-note-search-bar button.Stashy-tool-btn:hover:not(:disabled) { background-color: #eee; transform: none; }
#Stashy-in-note-search-bar button.Stashy-tool-btn:disabled { opacity: 0.5; background-color: #f5f5f5; }
#Stashy-in-note-search-count { font-size: 12px; color: var(--Stashy-text-light); min-width: 40px; text-align: center; white-space: nowrap; }

/* --- Screenshot Annotation Modal Styles (Adapt from Diagram) --- */
#Stashy-screenshot-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  padding: 15px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.25);
  z-index: 10000;
  border-radius: 12px;
  /* width/height/max set dynamically or in JS */
  display: none; /* Changed from flex to none initial */
  flex-direction: column;
  gap: 10px; /* Reduced gap */
  font-family: Arial, sans-serif;
  border: 1px solid #bbb;
  box-sizing: border-box;
}

#Stashy-screenshot-modal h4 {
  cursor: move;
  text-align: center;
  margin: 0 0 5px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
  font-size: 16px;
  user-select: none;
}

/* Container for the canvas, allows scrolling */
#Stashy-screenshot-modal div[style*="flex-grow: 1"] {
  position: relative; /* For potential overlays like text input */
  background-color: #f0f0f0; /* Different background */
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: auto; /* Changed from hidden to auto */
  flex-grow: 1;
}

#Stashy-screenshot-modal canvas {
  background: #ffffff;
  cursor: crosshair;
  display: block; /* Important */
  /* Width/height set by JS */
}

#Stashy-screenshot-toolbar {
  display: flex;
  gap: 5px;
  padding: 8px;
  background: #e8ecef;
  border-radius: 8px;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #ddd;
  flex-shrink: 0;
}

#Stashy-screenshot-toolbar > div {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Style annotation tool buttons */
#Stashy-screenshot-toolbar button.Stashy-tool-btn {
  font-size: 14px;
  padding: 5px 4px;
  background-color: #fff;
  border: 1px solid #ccc;
  min-width: 30px; /* Ensure clickable area */
}
#Stashy-screenshot-toolbar button.Stashy-tool-btn:hover:not(:disabled),
#Stashy-screenshot-toolbar button.Stashy-tool-btn:focus-visible {
  background-color: #e9ecef;
  border-color: #bbb;
}
#Stashy-screenshot-toolbar button.Stashy-tool-btn:disabled {
  opacity: 0.5; cursor: not-allowed; background-color: #eee; transform: none; box-shadow: none;
}
/* Active state for annotation tools */
#Stashy-screenshot-toolbar button.Stashy-tool-btn.active {
  background: var(--Stashy-accent);
  color: #ffffff;
  border-color: var(--Stashy-accent-darker);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
}
/* Hide text span for screenshot toolbar buttons specifically */
#Stashy-screenshot-toolbar button.Stashy-tool-btn .Stashy-text {
  display: none;
}

/* Inherit color picker styles if needed */
#Stashy-screenshot-toolbar .Stashy-color-picker {
  /* Styles inherited from diagram editor section */
   width: 30px; height: 30px; border: 1px solid #ced4da; border-radius: 4px; cursor: pointer; padding: 0; vertical-align: middle;
}
#Stashy-screenshot-toolbar .Stashy-color-picker::-webkit-color-swatch-wrapper { padding: 2px; }
#Stashy-screenshot-toolbar .Stashy-color-picker::-webkit-color-swatch { border: none; border-radius: 3px; }
#Stashy-screenshot-toolbar .Stashy-color-picker::-moz-color-swatch { border: none; border-radius: 3px; }

/* Style for the temporary text input overlay */
#Stashy-screenshot-modal input[type="text"][style*="position: absolute"] {
  border: 1px dashed #777;
  padding: 3px 5px;
  font-size: 14px;
  font-family: sans-serif;
  background-color: rgba(255, 255, 255, 0.85);
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  z-index: 10001; /* Above canvas */
  outline: none;
}

/* Style for the link inserted into the note */
.Stashy-drive-link.screenshot-link {
    display: inline-block;
    padding: 3px 8px;
    border: 1px solid var(--Stashy-border);
    border-radius: 4px;
    background-color: var(--Stashy-timestamp-hover-bg); /* Reuse a light bg */
    color: var(--Stashy-timestamp-color); /* Reuse link color */
    text-decoration: none;
    font-size: 0.9em;
    cursor: help; /* Indicate it might not be directly clickable */
}
.Stashy-drive-link.screenshot-link:hover {
    background-color: #d0eaff;
    border-color: #a8d4ff;
}
.Stashy-drive-link.screenshot-link img { /* Style potential icon */
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
    width: 16px;
    height: 16px;
}

.Stashy-screenshot-modal-custom { /* Or use .Stashy-diagram-editor */
  position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
  background: #ffffff; padding: 15px; box-shadow: 0 6px 25px rgba(0, 0, 0, 0.25);
  z-index: 10000; border-radius: 12px; display: none; flex-direction: column;
  gap: 10px; font-family: Arial, sans-serif; border: 1px solid #bbb;
  box-sizing: border-box; width: auto; height: auto; max-width: 90vw; max-height: 90vh;
}
/* Title */
#Stashy-screenshot-modal-custom h4, /* Adjust selector if needed */
.Stashy-diagram-editor h4 {
  cursor: move; text-align: center; margin: 0 0 5px 0; padding-bottom: 5px;
  border-bottom: 1px solid #eee; font-size: 16px; user-select: none;
}
/* Canvas Container */
#Stashy-screenshot-modal-custom div[style*="flex-grow: 1"], /* Adjust selector if needed */
.Stashy-diagram-editor div[style*="flex-grow: 1"] {
  position: relative; background-color: #f0f0f0; border: 1px solid #ccc;
  border-radius: 4px; overflow: auto; flex-grow: 1;
}
/* Canvas */
#Stashy-screenshot-canvas {
  background: #ffffff; cursor: crosshair; display: block;
}
/* Toolbar */
#Stashy-screenshot-toolbar { /* Use the ID */
  display: flex; gap: 5px; padding: 8px; background: #e8ecef;
  border-radius: 8px; justify-content: space-between; align-items: center;
  border: 1px solid #ddd; flex-shrink: 0; flex-wrap: wrap; /* Allow wrapping */
}
#Stashy-screenshot-toolbar > div { /* Left/Right sections */
  display: flex; align-items: center; gap: 5px; flex-wrap: wrap; /* Allow wrapping */
}
/* Buttons in Screenshot Toolbar (reuse basic tool button style) */
#Stashy-screenshot-toolbar button.Stashy-tool-btn {
  font-size: 16px; /* Slightly larger icons maybe */
  padding: 5px 6px;
  background-color: #fff;
  border: 1px solid #ccc;
  min-width: 32px; /* Ensure buttons have some width */
}
/* Active state for tools */
#Stashy-screenshot-toolbar button.Stashy-tool-btn.active {
  background: var(--primary-color, #34D399); /* Use CSS variable */
  color: #ffffff;
  border-color: var(--primary-color-darker, #059669);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
}
/* Hide text span in screenshot toolbar specifically */
#Stashy-screenshot-toolbar button.Stashy-tool-btn .Stashy-text {
  display: none;
}

/* Annotation Color Palette Swatches */
.Stashy-annotation-color-swatch {
  width: 22px; height: 22px; border-radius: 50%; padding: 0;
  border: 1px solid #999; /* Slightly darker border */
  cursor: pointer; box-shadow: var(--shadow-sm);
  transition: transform 0.1s ease, box-shadow 0.1s ease, border-color 0.1s ease;
}
.Stashy-annotation-color-swatch:hover {
  transform: scale(1.15);
  box-shadow: var(--shadow-md);
  border-color: #555;
}
.Stashy-annotation-color-swatch.active {
  border-width: 2px;
  border-color: #000000; /* Black border for active */
  transform: scale(1.1);
  box-shadow: inset 0 0 0 2px white; /* Inner white ring */
}

/* Line Width Slider (reuse diagram slider style) */
#Stashy-screenshot-toolbar .Stashy-brush-slider {
  width: 70px; height: 10px; /* Make slider slightly thinner */
  accent-color: var(--primary-color, #34D399);
  vertical-align: middle;
  cursor: pointer;
}
#Stashy-screenshot-toolbar input[type=range]::-webkit-slider-thumb {
  height: 16px; width: 16px; /* Adjust thumb size */
}
#Stashy-screenshot-toolbar input[type=range]::-moz-range-thumb {
   height: 16px; width: 16px; /* Adjust thumb size */
}

/* Temporary Text Input Overlay */
.Stashy-temp-text-input {
  border: 1px dashed #555 !important; padding: 4px !important; font-size: 14px !important;
  font-family: sans-serif !important; background-color: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important; z-index: 11 !important;
  outline: none !important; position: absolute !important; /* Ensure position is absolute */
  min-width: 80px !important;
  /* Color is set by JS */
}


/* Style for the link inserted into the note */
#Stashy-text .Stashy-drive-link.screenshot-link {
    display: inline-block; padding: 3px 8px; border: 1px solid var(--border-color);
    border-radius: 4px; background-color: var(--secondary-color-lighter);
    color: var(--secondary-color-darker); text-decoration: none;
    font-size: 0.9em; cursor: default; /* Default cursor, not directly actionable */
    margin: 2px 0;
}
#Stashy-text .Stashy-drive-link.screenshot-link:hover {
    background-color: var(--secondary-color-light); border-color: var(--secondary-color);
}
/* Style placeholder for local screenshot */
#Stashy-text p em { /* Target the emphasis tag used in the placeholder */
    color: var(--text-secondary);
    background-color: var(--secondary-color-lighter);
    padding: 2px 5px;
    border-radius: 3px;
    font-style: normal; /* Override default emphasis italic */
}

.Stashy-quick-snippets-toolbar .screenshot-options-container {
  display: inline-flex; /* Align checkboxes horizontally */
  align-items: center;  /* Center items vertically */
  gap: 8px;             /* Space between options */
  margin-left: 8px;     /* Space after the main screenshot button */
  padding: 2px 4px;     /* Optional small padding */
  border-left: 1px solid var(--Stashy-border, #e5e7eb); /* Optional separator */
  height: 100%; /* Try to match button height */
}

.screenshot-options-container label {
  display: inline-flex;
  align-items: center;
  font-size: 11px;      /* Smaller font size for options */
  color: var(--Stashy-text-light, #6b7280); /* Lighter text color */
  cursor: pointer;
  user-select: none;    /* Prevent selecting the label text */
  white-space: nowrap; /* Prevent labels wrapping */
}

.screenshot-options-container input[type="checkbox"] {
  width: 14px;         /* Smaller checkbox */
  height: 14px;
  margin-right: 3px;   /* Space between checkbox and label text */
  vertical-align: middle; /* Align checkbox with text */
  cursor: pointer;
  accent-color: var(--Stashy-accent, #7ff362); /* Use accent color */
}

.screenshot-options-container label:hover {
  color: var(--Stashy-text-color, #1f2937); /* Darken text on hover */
}

/* --- Styles for Screenshot Section in Insert Dropdown --- */
.Stashy-dropdown-content .Stashy-dropdown-group {
  /* Existing group styles apply */
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid var(--Stashy-border, #e5e7eb);
}
.Stashy-dropdown-content .Stashy-dropdown-group:first-child {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}
.Stashy-dropdown-content .Stashy-dropdown-group-title {
  /* Existing title styles apply */
  font-size: 10px;
  font-weight: 600;
  color: var(--Stashy-text-light, #6b7280);
  padding: 2px 4px;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Style the main screenshot button within the dropdown */
.Stashy-dropdown-screenshot-btn {
  display: flex; /* Use flex to control layout */
  align-items: center; /* Center content vertically */
  width: 100%;
  text-align: left;
  padding: 8px 10px;
  background-color: transparent;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 13px; /* Standard dropdown item size */
  justify-content: flex-start; /* Align icon/text left */
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Show ellipsis for overflow */
  max-width: 100%; /* Ensure it doesn't exceed container */
}
.Stashy-dropdown-screenshot-btn .Stashy-icon {
  margin-right: 8px; /* Space between icon and text */
  flex-shrink: 0; /* Prevent icon from shrinking */
}
.Stashy-dropdown-screenshot-btn .Stashy-text {
  flex-shrink: 1; /* Allow text to shrink if needed */
  overflow: hidden; /* Hide text overflow */
  text-overflow: ellipsis; /* Show ellipsis for long text */
}
.Stashy-dropdown-screenshot-btn:hover {
  background-color: var(--btn-bg-hover, #f3f4f6); /* Standard dropdown hover */
  transform: none; /* No lift effect inside dropdown */
}

/* Container for the checkboxes below the button */
.Stashy-dropdown-screenshot-options {
  padding: 4px 10px 4px 28px; /* Indent options slightly */
  display: flex;
  flex-direction: column; /* Stack checkboxes vertically */
  gap: 4px; /* Space between checkboxes */
  margin-top: 4px; /* Space below the main button */
}

/* Style for the label containing the checkbox */
.Stashy-dropdown-checkbox-label {
  display: flex;         /* Use flex for alignment */
  align-items: center;   /* Center checkbox and text vertically */
  font-size: 12px;       /* Slightly smaller font */
  color: var(--Stashy-text-light, #6b7280);
  cursor: pointer;
  user-select: none;
  padding: 2px 0;        /* Small vertical padding */
}
.Stashy-dropdown-checkbox-label:hover {
  color: var(--Stashy-text-color, #1f2937);
}

/* Style for the checkbox itself */
.Stashy-dropdown-checkbox {
  width: 14px;
  height: 14px;
  margin-right: 6px; /* Space after checkbox */
  vertical-align: middle; /* Ensure alignment */
  cursor: pointer;
  accent-color: var(--Stashy-accent, #34D399); /* Use accent color */
}




/* Quick Snippet Button */
.Stashy-quick-snippet-btn {
  position: absolute;
  z-index: 2147483646; /* One less than max to stay below feedback */
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #cccccc;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  padding: 0;
  margin: 0;
  outline: none;

  /* Performance optimizations */
  contain: layout style; /* Lighter containment for small elements */
  will-change: transform; /* Hint to browser to optimize transform property */
  backface-visibility: hidden; /* Prevent flickering in some browsers */
}

.Stashy-quick-snippet-btn:hover {
  background-color: #f0f0f0;
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.Stashy-quick-snippet-btn:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Snippet feedback notification */
.Stashy-snippet-feedback {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 2147483647;
  padding: 10px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: opacity 0.3s ease-in-out;
  font-family: var(--Stashy-font-family);
  font-size: 14px;
}

.Stashy-snippet-feedback.success {
  background-color: #4CAF50;
  color: white;
}

.Stashy-snippet-feedback.error {
  background-color: #f44336;
  color: white;
}

/* ===================================== */
/*      Collaboration Styles             */
/* ===================================== */

/* Sharing Panel Overlay */
.Stashy-sharing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2147483646;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.Stashy-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
    color: #6b7280;
}

.Stashy-close-btn:hover {
    background-color: #f3f4f6;
    color: #111827;
}

/* Panel Content */
.Stashy-panel-content {
    padding: 24px;
    flex: 1;
    overflow-y: auto;
}

.Stashy-content-preview {
    margin-bottom: 24px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.Stashy-content-preview h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
}

.Stashy-content-text {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
    max-height: 100px;
    overflow-y: auto;
}

/* Sharing Options */
.Stashy-sharing-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.Stashy-option-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.Stashy-option-group label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.Stashy-option-group select,
.Stashy-option-group input[type="number"],
.Stashy-option-group input[type="datetime-local"],
.Stashy-option-group input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.Stashy-option-group select:focus,
.Stashy-option-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Panel Footer */
.Stashy-panel-footer {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #f9fafb;
}

.Stashy-btn-primary,
.Stashy-btn-secondary {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid;
}

.Stashy-btn-primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.Stashy-btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.Stashy-btn-secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;
}

.Stashy-btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}



/* Presence Indicator */
.Stashy-presence-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 2147483645;
    font-family: var(--Stashy-font-family);
    font-size: 12px;
    max-width: 200px;
}

.Stashy-presence-indicator .presence-header {
    font-weight: 600;
    margin-bottom: 4px;
    color: #333;
}

.Stashy-presence-indicator .presence-user {
    display: flex;
    align-items: center;
    margin: 2px 0;
    color: #666;
}

.Stashy-presence-indicator .user-avatar {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 6px;
    display: inline-block;
}

/* ===================================== */
/*         Smart Templates Styles        */
/* ===================================== */

/* Legacy template editor styles removed - now using Visual Template Builder */

/* Template content wrapper - clean styling without logo and border */
.Stashy-template-content {
    margin: 8px 0;
    position: relative;
}

/* Template editor dropdown styles */
.Stashy-dropdown-checkbox-label {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.Stashy-dropdown-checkbox-label:hover {
    background-color: #f0f0f0;
}

.Stashy-dropdown-checkbox {
    margin-right: 6px;
}

/* Smart template processing indicator */
.smart-template-processing {
    position: relative;
}

.smart-template-processing::after {
    content: "⚡";
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
    animation: smart-template-pulse 1s infinite;
}

@keyframes smart-template-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Template editor syntax highlighting */
.template-syntax-prompt {
    background: rgba(66, 133, 244, 0.1);
    color: #1565c0;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 500;
}

.template-syntax-condition {
    background: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 500;
}

.template-syntax-date {
    background: rgba(255, 152, 0, 0.1);
    color: #ef6c00;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 500;
}

/* Template preview styles */
.template-preview-placeholder {
    background: #e3f2fd;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
    color: #1565c0;
    border: 1px solid #bbdefb;
}

.template-preview-condition {
    border-left: 3px solid #4caf50;
    padding-left: 8px;
    margin: 4px 0;
    background: rgba(76, 175, 80, 0.05);
}

.template-preview-date {
    background: #fff3e0;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
    color: #ef6c00;
    border: 1px solid #ffcc80;
}

/* Template editor help section */
.template-help-code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #333;
}

/* Smart template status indicators */
.template-status-success {
    color: #4caf50;
    font-weight: 500;
}

.template-status-error {
    color: #f44336;
    font-weight: 500;
}

.template-status-processing {
    color: #ff9800;
    font-weight: 500;
}

/* Old template editor button and modal styles removed - now using Visual Template Builder */

/* Table builder styles */
#Stashy-table-builder-modal .table-size-selector div:hover {
    background: #4285f4 !important;
}

/* Smart table styles */
.Stashy-smart-table {
    margin: 10px 0;
    font-family: Arial, sans-serif;
}

.Stashy-smart-table.table-bordered {
    border: 2px solid #ddd;
}

.Stashy-smart-table.table-bordered th,
.Stashy-smart-table.table-bordered td {
    border: 1px solid #ddd !important;
}

.Stashy-smart-table.table-striped tr:nth-child(even) {
    background-color: #f9f9f9;
}

.Stashy-smart-table.table-data {
    background: #fafafa;
    border: 1px solid #e0e0e0;
}

.Stashy-smart-table.table-data th {
    background: #f0f0f0;
    font-weight: bold;
    text-align: center;
}

.Stashy-smart-table.table-responsive {
    overflow-x: auto;
    display: block;
    white-space: nowrap;
}

/* Syntax highlighting enhancements */
#syntax-highlight-overlay {
    background: rgba(255, 255, 255, 0.95);
}

/* Old template management panel styles removed - now using Visual Template Builder */

/* Table preview enhancements */
#table-preview table {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Old quick insert toolbar styles removed - now using Visual Template Builder */

/* Template validation indicators */
.template-validation-error {
    border-left: 3px solid #f44336;
    background: rgba(244, 67, 54, 0.05);
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 4px;
    font-size: 12px;
    color: #d32f2f;
}

.template-validation-warning {
    border-left: 3px solid #ff9800;
    background: rgba(255, 152, 0, 0.05);
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 4px;
    font-size: 12px;
    color: #f57c00;
}

.template-validation-success {
    border-left: 3px solid #4caf50;
    background: rgba(76, 175, 80, 0.05);
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 4px;
    font-size: 12px;
    color: #388e3c;
}

/* Dynamic Table Styles */
.dynamic-table th,
.dynamic-table td {
    transition: background-color 0.2s ease;
}

.dynamic-table th:hover,
.dynamic-table td:hover {
    background-color: rgba(102, 126, 234, 0.1) !important;
}

.smart-table th {
    position: relative;
    user-select: none;
}

.smart-table th:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Table notification animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Enhanced table controls */
.dynamic-table-controls {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.dynamic-table-controls button {
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 500;
}

.dynamic-table-controls button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Smart table features indicator */
.smart-features-indicator {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #1565c0;
}

/* Table cell editing enhancements */
.dynamic-table td[contenteditable="true"]:focus,
.smart-table td[contenteditable="true"]:focus {
    outline: 2px solid #667eea;
    outline-offset: -2px;
    background-color: rgba(102, 126, 234, 0.05);
}

.dynamic-table th[contenteditable="true"]:focus,
.smart-table th[contenteditable="true"]:focus {
    outline: 2px solid #667eea;
    outline-offset: -2px;
    background-color: rgba(255, 255, 255, 0.3);
}

/* Enhanced table styles for note text area */
.enhanced-table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
    border: 1px solid #ddd;
}

.enhanced-table th,
.enhanced-table td {
    border: 1px solid #ddd;
    padding: 8px;
    transition: background-color 0.2s ease;
}

.enhanced-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    position: relative;
}

.enhanced-table th:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.enhanced-table td:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.enhanced-table td[contenteditable="true"]:focus,
.enhanced-table th[contenteditable="true"]:focus {
    outline: 2px solid #667eea;
    outline-offset: -2px;
    background-color: rgba(102, 126, 234, 0.1);
}

/* Table controls container */
.table-controls-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    align-items: center;
}

.table-controls-container::before {
    content: "Table Controls:";
    font-size: 11px;
    font-weight: bold;
    color: #6c757d;
    margin-right: 8px;
}

/* Table control buttons */
.table-controls-container button {
    background: #007bff;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.table-controls-container button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.table-controls-container button:active {
    transform: translateY(0);
}

/* Auto-enhancement indicator */
.enhanced-table::after {
    content: "";
    position: absolute;
    top: -5px;
    right: -5px;
    width: 10px;
    height: 10px;
    background: #28a745;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    z-index: 1;
}

/* Responsive table controls */
@media (max-width: 768px) {
    .table-controls-container {
        flex-direction: column;
        align-items: stretch;
    }

    .table-controls-container button {
        margin: 2px 0;
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* --- END OF FILE content.css --- */

/* --- Resize Handles --- */
.Stashy-resizer {
  position: absolute;
  width: 10px;
  height: 10px;
  background: transparent; /* Make them invisible, rely on cursor */
  z-index: 10000; /* Above note content */
}
/* Corners */
.Stashy-resizer-top-left { top: -5px; left: -5px; cursor: nwse-resize; }
.Stashy-resizer-top-right { top: -5px; right: -5px; cursor: nesw-resize; }
.Stashy-resizer-bottom-left { bottom: -5px; left: -5px; cursor: nesw-resize; }
.Stashy-resizer-bottom-right { bottom: -5px; right: -5px; cursor: nwse-resize; }
/* Edges */
.Stashy-resizer-top { top: -5px; left: 50%; transform: translateX(-50%); width: calc(100% - 10px); height: 10px; cursor: ns-resize; }
.Stashy-resizer-bottom { bottom: -5px; left: 50%; transform: translateX(-50%); width: calc(100% - 10px); height: 10px; cursor: ns-resize; }
.Stashy-resizer-left { left: -5px; top: 50%; transform: translateY(-50%); height: calc(100% - 10px); width: 10px; cursor: ew-resize; }
.Stashy-resizer-right { right: -5px; top: 50%; transform: translateY(-50%); height: calc(100% - 10px); width: 10px; cursor: ew-resize; }

#Stashy-text .Stashy-template-content {
  font-size: 0.9em; /* 90% of the parent's (#Stashy-text) font size */
  /* You could also use a fixed pixel value smaller than the default, e.g., 12px */
  /* font-size: 12px; */
  line-height: 1.5; /* Maintain good line spacing */
  /* Add any other styles specific to template blocks here */
}

/* --- Optional: Adjust margin for direct children inside template --- */
/* This prevents double margins if a template starts with <p> or <h3> */
#Stashy-text .Stashy-template-content > *:first-child {
  margin-top: 0;
}
#Stashy-text .Stashy-template-content > *:last-child {
  margin-bottom: 0;
}

/* --- Screenshot Text Input Overlay Styles --- */
.Stashy-temp-text-input {
  min-width: 100px;
  min-height: 30px;
  padding: 5px 8px;
  border: 2px dashed #0066cc;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.9);
  font-family: var(--Stashy-font-family);
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 11000;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.Stashy-temp-text-input:focus {
  outline: none;
  border-color: #0099ff;
  box-shadow: 0 2px 12px rgba(0, 153, 255, 0.3);
}

/* Enhanced screenshot canvas container with scroll support */
#Stashy-screenshot-canvas-container {
  overflow: auto !important; /* Enable scrolling for large screenshots */
  position: relative;
  /* Flexible sizing for different screenshot types */
  width: 100%;
  height: 100%;
  /* Scrollbar styling - invisible by default, visible on hover */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent transparent; /* Firefox - invisible by default */
  -ms-overflow-style: -ms-autohiding-scrollbar; /* IE and Edge - auto-hiding */
}

/* Enhanced annotation toolbar styling - Compact */
.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar {
  padding: 8px 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 4px;
  min-height: 50px;
}

/* Enhanced tool button styling - Compact */
.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar button {
  min-width: 28px;
  height: 28px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar button:hover {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33,150,243,0.2);
}

.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar button.active {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border-color: #1565c0;
  box-shadow: 0 4px 8px rgba(33,150,243,0.3);
}

.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
  border-color: #e9ecef;
  transform: none;
  box-shadow: none;
}

/* Remove any conflicting button styles for color swatches */
.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar button[data-color] {
  background: none !important; /* Let inline styles take precedence */
  border: none !important; /* Let inline styles take precedence */
  min-width: auto !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
}

.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar button[data-color]:hover {
  background: none !important; /* Prevent hover from overriding color */
}

/* Enhanced line width slider */
.Stashy-brush-slider {
  width: 80px !important;
  height: 6px !important;
  border-radius: 3px !important;
  background: linear-gradient(to right, #e9ecef 0%, #ced4da 100%) !important;
  outline: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.Stashy-brush-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.Stashy-brush-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(33,150,243,0.3);
}

.Stashy-brush-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Toolbar separators */
.Stashy-screenshot-modal-custom .Stashy-diagram-toolbar > div > div {
  background: linear-gradient(to bottom, transparent 0%, #ced4da 20%, #ced4da 80%, transparent 100%);
  opacity: 0.6;
}

/* Enhanced modal styling */
.Stashy-screenshot-modal-custom {
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(0,0,0,0.05) !important;
  overflow: hidden !important;
  backdrop-filter: blur(10px) !important;
}

.Stashy-screenshot-modal-custom h4 {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
  color: white !important;
  margin: 0 !important;
  padding: 16px 20px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  border-bottom: none !important;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
}

/* Enhanced canvas styling */
#Stashy-screenshot-canvas {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
  /* Enhanced image rendering for maximum quality */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  /* Fallback for older browsers */
  -ms-interpolation-mode: nearest-neighbor;
  /* Ensure sharp pixel boundaries */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

#Stashy-screenshot-canvas:hover {
  box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

/* Eraser mode styling */
.Stashy-screenshot-modal-custom .eraser-active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  color: white !important;
  border-color: #dc3545 !important;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
}

/* Measurement tool styling */
.measurement-display {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid #2196f3;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: bold;
  color: #1976d2;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Advanced shape styling */
.shape-preview {
  stroke-dasharray: 5,5;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to { stroke-dashoffset: -10; }
}

/* WebKit scrollbar styles for screenshot container */
#Stashy-screenshot-canvas-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
}

#Stashy-screenshot-canvas-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

#Stashy-screenshot-canvas-container::-webkit-scrollbar-corner {
  background-color: transparent;
}

/* Show scrollbar only on hover */
#Stashy-screenshot-canvas-container:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}

#Stashy-screenshot-canvas-container:hover {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox - visible on hover */
}

/* Smooth scrolling behavior */
#Stashy-screenshot-canvas-container {
  scroll-behavior: smooth;
}

/* Enhanced screenshot modal sizing and dragging */
#Stashy-screenshot-modal {
  max-width: 90vw !important;
  max-height: 90vh !important;
  min-width: 400px;
  min-height: 300px;
  resize: both; /* Allow manual resizing */
  overflow: hidden; /* Prevent modal content overflow */
  position: fixed !important; /* Ensure proper positioning for dragging */
  z-index: 10000; /* Ensure modal stays on top */
}

/* Screenshot modal title bar styling */
#Stashy-screenshot-title {
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-bottom: 1px solid #ccc;
  cursor: move;
  user-select: none;
  padding: 10px 15px;
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  text-align: center;
  border-radius: 8px 8px 0 0; /* Rounded top corners */
  transition: background-color 0.2s ease;
}

#Stashy-screenshot-title:hover {
  background: linear-gradient(135deg, #e8e8e8 0%, #d8d8d8 100%);
}

#Stashy-screenshot-title:active {
  background: linear-gradient(135deg, #d0d0d0 0%, #c0c0c0 100%);
  cursor: grabbing;
}

/* Ensure canvas container takes available space */
#Stashy-screenshot-modal div[style*="flex-grow: 1"] {
  min-height: 200px; /* Minimum height for usability */
  max-height: 70vh; /* Limit height to enable scrolling */
}

/* Calendar integration styles */
.Stashy-calendar-option {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  padding: 6px;
  border-radius: 4px;
  z-index: 100;
  position: relative;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.2s ease;
}

.Stashy-calendar-option:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.Stashy-calendar-option input[type="checkbox"] {
  margin-right: 6px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  z-index: 101;
  position: relative;
  pointer-events: none; /* Let the wrapper handle clicks */
  accent-color: #2196F3; /* Blue color for checkbox */
  filter: hue-rotate(0deg); /* Ensure blue color is applied */
}

.Stashy-calendar-option label {
  color: var(--Stashy-text-light);
  cursor: pointer;
  z-index: 101;
  position: relative;
  user-select: none;
  pointer-events: none; /* Let the wrapper handle clicks */
  font-weight: 500;
}

/* --- Premium Feature Styles --- */

/* Premium user styling */
.stashy-premium-user .premium-badge {
  display: inline-block;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b5a00;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 8px;
  margin-left: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Free user styling */
.stashy-free-user .premium-badge {
  display: none;
}

/* Premium locked features */
.premium-locked {
  opacity: 0.6;
  position: relative;
  cursor: not-allowed !important;
}

.premium-locked::after {
  content: "🔒";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 4px;
  border-radius: 4px;
  z-index: 1000;
}

.premium-locked:hover::before {
  content: "Requires Stashy Pro";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1001;
  margin-bottom: 5px;
}

/* Upgrade prompt button */
.upgrade-prompt-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  margin: 4px;
}

.upgrade-prompt-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Usage counter display */
.usage-counter {
  font-size: 11px;
  color: var(--Stashy-text-light);
  margin-left: 8px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

.usage-counter.warning {
  background: #fef3c7;
  color: #d97706;
}

.usage-counter.limit-reached {
  background: #fee2e2;
  color: #dc2626;
}

/* Premium feature indicators */
.feature-premium-indicator {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 9px;
  font-weight: 600;
  padding: 1px 4px;
  border-radius: 4px;
  margin-left: 4px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.stashy-premium-user .feature-premium-indicator {
  background: linear-gradient(135deg, #10b981, #059669);
}

/* Free tier limitation messages */
.free-tier-message {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  font-size: 13px;
  color: var(--Stashy-text-color);
}

.free-tier-message .upgrade-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.free-tier-message .upgrade-link:hover {
  text-decoration: underline;
}

/* Premium status indicator in toolbar */
.stashy-toolbar .premium-status {
  display: flex;
  align-items: center;
  font-size: 11px;
  margin-left: auto;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.stashy-toolbar .premium-status.premium {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b5a00;
}

.stashy-toolbar .premium-status.free {
  background: #f3f4f6;
  color: var(--Stashy-text-light);
}

/* ===================================== */
/*       Web Scraper Modal Styles       */
/* ===================================== */

/* Modal overlay */
.stashy-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 10002;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: var(--Stashy-font-family);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stashy-modal-overlay.stashy-modal-show {
    opacity: 1;
}

/* Modal content */
.stashy-modal-content {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 16px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: scale(0.95);
    transition: transform 0.3s ease-out;
}

.stashy-modal-overlay.stashy-modal-show .stashy-modal-content {
    transform: scale(1);
}

/* Modal header */
.stashy-modal-header {
    background: linear-gradient(135deg, var(--Stashy-accent) 0%, var(--Stashy-accent-darker) 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stashy-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.stashy-modal-icon {
    font-size: 24px;
}

.stashy-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.stashy-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Modal body */
.stashy-modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

.stashy-webscraper-instructions {
    margin-bottom: 20px;
    color: var(--Stashy-text-color);
    font-size: 14px;
    line-height: 1.5;
}

.stashy-webscraper-instructions p {
    margin: 0;
}

/* Categories container */
.stashy-webscraper-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.stashy-webscraper-category {
    border: 2px solid var(--Stashy-border);
    border-radius: 12px;
    transition: all 0.2s ease;
    background: #ffffff;
    overflow: hidden;
}

.stashy-webscraper-category:hover {
    border-color: var(--Stashy-accent);
    box-shadow: 0 4px 12px rgba(127, 243, 98, 0.15);
    transform: translateY(-2px);
}

.stashy-webscraper-category-label {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    cursor: pointer;
    margin: 0;
    gap: 12px;
    transition: background-color 0.2s ease;
}

.stashy-webscraper-category-label:hover {
    background-color: #f8f9fa;
}

.stashy-webscraper-checkbox {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: var(--Stashy-accent);
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 2px;
}

.stashy-webscraper-category-icon {
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 1px;
}

.stashy-webscraper-category-info {
    flex: 1;
    min-width: 0;
}

.stashy-webscraper-category-name {
    font-weight: 600;
    color: var(--Stashy-text-color);
    margin-bottom: 4px;
    font-size: 15px;
}

.stashy-webscraper-category-desc {
    color: var(--Stashy-text-light);
    font-size: 13px;
    line-height: 1.4;
}

/* Modal footer */
.stashy-modal-footer {
    padding: 20px 25px;
    background-color: #f8f9fa;
    border-top: 1px solid var(--Stashy-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.stashy-webscraper-footer-left,
.stashy-webscraper-footer-right {
    display: flex;
    gap: 10px;
}

/* Button styles */
.stashy-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: var(--Stashy-font-family);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.stashy-btn-primary {
    background: linear-gradient(135deg, var(--Stashy-accent) 0%, var(--Stashy-accent-darker) 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(127, 243, 98, 0.3);
}

.stashy-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(127, 243, 98, 0.4);
}

.stashy-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(127, 243, 98, 0.3);
}

.stashy-btn-secondary {
    background: #ffffff;
    color: var(--Stashy-text-color);
    border: 1px solid var(--Stashy-border);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stashy-btn-secondary:hover {
    background-color: #f8f9fa;
    border-color: var(--Stashy-accent);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.stashy-btn-secondary:active {
    transform: translateY(0);
    background-color: #e9ecef;
}

/* Responsive design */
@media (max-width: 768px) {
    .stashy-modal-content {
        width: 95%;
        max-height: 90vh;
    }

    .stashy-webscraper-categories {
        grid-template-columns: 1fr;
    }

    .stashy-modal-footer {
        flex-direction: column;
        gap: 15px;
    }

    .stashy-webscraper-footer-left,
    .stashy-webscraper-footer-right {
        width: 100%;
        justify-content: center;
    }
}

/* --- END OF FILE content.css --- */